import { create } from 'zustand';

import { getServerStatuses } from '@/actions/server-status';
import {
  ServerStatusActions,
  ServerStatusStates,
  ServerStatusType,
} from '@/store/server-status/type';

export const useServerStatusStore = create<
  ServerStatusStates & ServerStatusActions
>(set => ({
  statuses: [],
  loading: false,
  setStatuses: (statuses: ServerStatusType[]) => set(() => ({ statuses })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  fetchStatuses: async () => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getServerStatuses();
      if (response?.status) {
        set(() => ({ statuses: response.data, loading: false }));
      } else {
        set(() => ({ statuses: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch server statuses:', error);
      set(() => ({ statuses: [], loading: false }));
    }
  },
}));
