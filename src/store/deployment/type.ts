export interface DeploymentStates {
  selectedDeployment: DeploymentType | null;
  loading: boolean;
  updating: boolean;
  creating: boolean;
  deleting: boolean;
  updatingStatus: boolean;
}

export interface DeploymentActions {
  setSelectedDeployment: (deployment: DeploymentType | null) => void;
  setLoading: (loading: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setCreating: (creating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  setUpdatingStatus: (updatingStatus: boolean) => void;
  fetchDeployment: (id: number) => Promise<void>;
  createDeployment: (data: CreateDeploymentRequest) => Promise<any>;
  updateDeployment: (id: number, data: UpdateDeploymentRequest) => Promise<any>;
  updateDeploymentStatus: (
    id: number,
    data: UpdateDeploymentStatusRequest
  ) => Promise<any>;
  deleteDeployment: (id: number) => Promise<any>;
}

export interface CreateDeploymentRequest {
  name: string;
  image: string;
  container_port: number;
  replicas: number;
  namespace_id: number;
}

export interface UpdateDeploymentRequest {
  name: string;
  image: string;
  container_port: number;
  namespace_id: number;
  replicas: number;
  status_id: number;
}

export interface UpdateDeploymentStatusRequest {
  status_id: number;
}

export type StatusType = {
  id: number;
  name: string;
};

export type NamespaceType = {
  id: number;
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
};

export type EnvironmentType = {
  id: number;
  name: string;
  value: string;
};

export type DeploymentType = {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  image: string;
  container_port: number;
  replicas: number;
  last_deployed: string;
  status: StatusType;
  namespace: NamespaceType;
  environments: EnvironmentType[];
};
