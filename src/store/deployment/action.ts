import { create } from 'zustand';

import {
  getDeployment,
  createDeployment as createDeployment<PERSON><PERSON>,
  updateDeployment as updateDeployment<PERSON><PERSON>,
  updateDeploymentStatus as updateDeploymentStatusAPI,
  deleteDeployment as deleteDeploymentAPI,
} from '@/actions/deployment';
import {
  DeploymentActions,
  DeploymentStates,
  DeploymentType,
  CreateDeploymentRequest,
  UpdateDeploymentRequest,
  UpdateDeploymentStatusRequest,
} from '@/store/deployment/type';

export const useDeploymentStore = create<DeploymentStates & DeploymentActions>(
  set => ({
    selectedDeployment: null,
    loading: false,
    updating: false,
    creating: false,
    deleting: false,
    updatingStatus: false,

    setSelectedDeployment: (deployment: DeploymentType | null) =>
      set(() => ({ selectedDeployment: deployment })),
    setLoading: (loading: boolean) => set(() => ({ loading })),
    setUpdating: (updating: boolean) => set(() => ({ updating })),
    setCreating: (creating: boolean) => set(() => ({ creating })),
    setDeleting: (deleting: boolean) => set(() => ({ deleting })),
    setUpdatingStatus: (updatingStatus: boolean) =>
      set(() => ({ updatingStatus })),

    fetchDeployment: async (id: number) => {
      try {
        set(() => ({ loading: true }));
        const response: any = await getDeployment(id);
        if (response?.status) {
          set(() => ({ selectedDeployment: response.data, loading: false }));
        } else {
          set(() => ({ selectedDeployment: null, loading: false }));
        }
      } catch (error) {
        console.error('Failed to fetch deployment:', error);
        set(() => ({ selectedDeployment: null, loading: false }));
      }
    },

    createDeployment: async (data: CreateDeploymentRequest) => {
      try {
        set(() => ({ creating: true }));
        const response: any = await createDeploymentAPI(data);
        set(() => ({ creating: false }));

        // Set the created deployment as selected if successful
        if (response?.status) {
          set(() => ({ selectedDeployment: response.data }));
        }

        return response;
      } catch (error) {
        console.error('Failed to create deployment:', error);
        set(() => ({ creating: false }));
        throw error;
      }
    },

    updateDeployment: async (id: number, data: UpdateDeploymentRequest) => {
      try {
        set(() => ({ updating: true }));
        const response: any = await updateDeploymentAPI(id, data);
        set(() => ({ updating: false }));

        // Update the selected deployment with the response data
        if (response?.status) {
          set(() => ({ selectedDeployment: response.data }));
        }

        return response;
      } catch (error) {
        console.error('Failed to update deployment:', error);
        set(() => ({ updating: false }));
        throw error;
      }
    },

    updateDeploymentStatus: async (
      id: number,
      data: UpdateDeploymentStatusRequest
    ) => {
      try {
        set(() => ({ updatingStatus: true }));
        const response: any = await updateDeploymentStatusAPI(id, data);
        set(() => ({ updatingStatus: false }));

        // Update the selected deployment with the response data
        if (response?.status) {
          set(() => ({ selectedDeployment: response.data }));
        }

        return response;
      } catch (error) {
        console.error('Failed to update deployment status:', error);
        set(() => ({ updatingStatus: false }));
        throw error;
      }
    },

    deleteDeployment: async (id: number) => {
      try {
        set(() => ({ deleting: true }));
        const response: any = await deleteDeploymentAPI(id);
        set(() => ({ deleting: false }));

        // Clear the selected deployment if it was the one being deleted
        if (response?.status) {
          set(state => ({
            selectedDeployment:
              state.selectedDeployment?.id === id
                ? null
                : state.selectedDeployment,
          }));
        }

        return response;
      } catch (error) {
        console.error('Failed to delete deployment:', error);
        set(() => ({ deleting: false }));
        throw error;
      }
    },
  })
);
