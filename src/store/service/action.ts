import { create } from 'zustand';

import {
  getServices,
  getService,
  createService as createServiceAP<PERSON>,
  updateService as updateServiceAP<PERSON>,
  updateServiceStatus as updateServiceStatusAPI,
  deleteService as deleteServiceAPI,
} from '@/actions/service';
import {
  ServiceActions,
  ServiceStates,
  ServiceType,
  CreateServiceRequest,
  PartialUpdateServiceRequest,
  UpdateServiceStatusRequest,
  ServiceFilters,
} from '@/store/service/type';

export const useServiceStore = create<ServiceStates & ServiceActions>(set => ({
  services: [],
  selectedService: null,
  loading: false,
  creating: false,
  updating: false,
  deleting: false,
  updatingStatus: false,

  setServices: (services: ServiceType[]) => set(() => ({ services })),
  setSelectedService: (service: ServiceType | null) =>
    set(() => ({ selectedService: service })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  setUpdating: (updating: boolean) => set(() => ({ updating })),
  setDeleting: (deleting: boolean) => set(() => ({ deleting })),
  setUpdatingStatus: (updatingStatus: boolean) =>
    set(() => ({ updatingStatus })),

  fetchServices: async (filters?: ServiceFilters) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getServices(filters);
      if (response?.status) {
        set(() => ({ services: response.data, loading: false }));
      } else {
        set(() => ({ services: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);
      set(() => ({ services: [], loading: false }));
    }
  },

  fetchService: async (id: number) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getService(id);
      if (response?.status) {
        set(() => ({ selectedService: response.data, loading: false }));
      } else {
        set(() => ({ selectedService: null, loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch service:', error);
      set(() => ({ selectedService: null, loading: false }));
    }
  },

  createService: async (data: CreateServiceRequest) => {
    try {
      set(() => ({ creating: true }));
      const response: any = await createServiceAPI(data);
      set(() => ({ creating: false }));

      // Set the created service as selected if successful
      if (response?.status) {
        set(() => ({ selectedService: response.data }));
      }

      return response;
    } catch (error) {
      console.error('Failed to create service:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },

  updateService: async (id: number, data: PartialUpdateServiceRequest) => {
    try {
      set(() => ({ updating: true }));
      const response: any = await updateServiceAPI(id, data);
      set(() => ({ updating: false }));

      // Update the selected service with the response data
      if (response?.status) {
        set(() => ({ selectedService: response.data }));
      }

      return response;
    } catch (error) {
      console.error('Failed to update service:', error);
      set(() => ({ updating: false }));
      throw error;
    }
  },

  updateServiceStatus: async (id: number, data: UpdateServiceStatusRequest) => {
    try {
      set(() => ({ updatingStatus: true }));
      const response: any = await updateServiceStatusAPI(id, data);
      set(() => ({ updatingStatus: false }));

      // Update the selected service with the response data
      if (response?.status) {
        set(() => ({ selectedService: response.data }));
      }

      return response;
    } catch (error) {
      console.error('Failed to update service status:', error);
      set(() => ({ updatingStatus: false }));
      throw error;
    }
  },

  deleteService: async (id: number) => {
    try {
      set(() => ({ deleting: true }));
      const response: any = await deleteServiceAPI(id);
      set(() => ({ deleting: false }));

      // Clear the selected service if it was deleted
      if (response?.status) {
        set(state => ({
          selectedService:
            state.selectedService?.id === id ? null : state.selectedService,
          services: state.services.filter(service => service.id !== id),
        }));
      }

      return response;
    } catch (error) {
      console.error('Failed to delete service:', error);
      set(() => ({ deleting: false }));
      throw error;
    }
  },
}));
