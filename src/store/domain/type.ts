export interface DomainStates {
  domains: DomainType[];
  selectedDomain: DomainType | null;
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
}

export interface DomainActions {
  setDomains: (domains: DomainType[]) => void;
  setSelectedDomain: (domain: DomainType | null) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  fetchDomains: (params: DomainQueryParams) => Promise<void>;
  fetchDomain: (id: number) => Promise<void>;
  createDomain: (data: CreateDomainRequest) => Promise<any>;
  updateDomainStatus: (
    id: number,
    data: UpdateDomainStatusRequest
  ) => Promise<any>;
  setDomainAsDefault: (
    id: number,
    data: SetDomainAsDefaultRequest
  ) => Promise<any>;
  deleteDomain: (id: number) => Promise<any>;
}

export interface DomainQueryParams {
  name?: string;
  is_default?: boolean;
  is_active?: boolean;
  namespace_id: number;
}

export interface NamespaceInfo {
  id: number;
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
}

export interface DomainType {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  is_default: boolean;
  is_active: boolean;
  zone_id: string;
  account_id: string;
  account_name: string;
  namespace_id: number;
  index: number;
  namespace: NamespaceInfo;
}

export interface CreateDomainRequest {
  name: string;
  is_default: boolean;
  is_active: boolean;
  zone_id: string;
  account_id: string;
  account_name: string;
  namespace_id: number;
}

export interface UpdateDomainStatusRequest {
  is_active: boolean;
}

export interface SetDomainAsDefaultRequest {
  is_redirect: boolean;
}

export interface DomainApiResponse {
  status: boolean;
  message: string;
  data: DomainType[];
}
