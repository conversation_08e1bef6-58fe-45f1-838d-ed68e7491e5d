import { create } from 'zustand';

import {
  getDomains,
  createDomain,
  getDomain,
  updateDomainStatus,
  setDomainAsDefault,
  deleteDomain,
} from '@/actions/domain';
import {
  DomainActions,
  DomainStates,
  DomainType,
  DomainQueryParams,
  CreateDomainRequest,
  UpdateDomainStatusRequest,
  SetDomainAsDefaultRequest,
} from '@/store/domain/type';

export const useDomainStore = create<DomainStates & DomainActions>(set => ({
  domains: [],
  selectedDomain: null,
  loading: false,
  creating: false,
  updating: false,
  deleting: false,

  setDomains: (domains: DomainType[]) => set(() => ({ domains })),
  setSelectedDomain: (domain: DomainType | null) =>
    set(() => ({ selectedDomain: domain })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  setUpdating: (updating: boolean) => set(() => ({ updating })),
  setDeleting: (deleting: boolean) => set(() => ({ deleting })),

  fetchDomains: async (params: DomainQueryParams) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getDomains(params);
      if (response?.status) {
        const domains = Array.isArray(response.data) ? response.data : [];
        set(() => ({ domains, loading: false }));
      } else {
        set(() => ({ domains: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch domains:', error);
      set(() => ({ domains: [], loading: false }));
    }
  },

  fetchDomain: async (id: number) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getDomain(id);
      if (response?.status) {
        set(() => ({ selectedDomain: response.data, loading: false }));
      } else {
        set(() => ({ selectedDomain: null, loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch domain:', error);
      set(() => ({ selectedDomain: null, loading: false }));
    }
  },

  createDomain: async (data: CreateDomainRequest) => {
    try {
      set(() => ({ creating: true }));
      const response: any = await createDomain(data);
      if (response?.status) {
        set(() => ({ creating: false }));
        return response;
      } else {
        set(() => ({ creating: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to create domain:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },

  updateDomainStatus: async (id: number, data: UpdateDomainStatusRequest) => {
    try {
      set(() => ({ updating: true }));
      const response: any = await updateDomainStatus(id, data);
      if (response?.status) {
        set(() => ({ updating: false }));
        return response;
      } else {
        set(() => ({ updating: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to update domain status:', error);
      set(() => ({ updating: false }));
      throw error;
    }
  },

  setDomainAsDefault: async (id: number, data: SetDomainAsDefaultRequest) => {
    try {
      set(() => ({ updating: true }));
      const response: any = await setDomainAsDefault(id, data);
      if (response?.status) {
        set(() => ({ updating: false }));
        return response;
      } else {
        set(() => ({ updating: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to set domain as default:', error);
      set(() => ({ updating: false }));
      throw error;
    }
  },

  deleteDomain: async (id: number) => {
    try {
      set(() => ({ deleting: true }));
      const response: any = await deleteDomain(id);
      if (response?.status) {
        set(() => ({ deleting: false }));
        return response;
      } else {
        set(() => ({ deleting: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to delete domain:', error);
      set(() => ({ deleting: false }));
      throw error;
    }
  },
}));
