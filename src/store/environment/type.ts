export interface EnvironmentStates {
  environments: EnvironmentType[];
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
}

export interface EnvironmentActions {
  setEnvironments: (environments: EnvironmentType[]) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  createEnvironment: (data: CreateEnvironmentRequest) => Promise<any>;
  updateEnvironment: (
    id: number,
    data: UpdateEnvironmentRequest
  ) => Promise<any>;
  deleteEnvironment: (id: number) => Promise<any>;
  addEnvironment: (environment: EnvironmentType) => void;
  removeEnvironment: (id: number) => void;
  updateEnvironmentInStore: (id: number, environment: EnvironmentType) => void;
}

export interface CreateEnvironmentRequest {
  name: string;
  value: string;
  deployment_id: number;
}

export interface UpdateEnvironmentRequest {
  name: string;
  value: string;
  deployment_id: number;
}

export type EnvironmentType = {
  id: number;
  name: string;
  value: string;
};
