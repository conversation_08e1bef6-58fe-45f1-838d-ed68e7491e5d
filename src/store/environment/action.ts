import { create } from 'zustand';

import {
  createEnvironment as createEnvironmentAP<PERSON>,
  updateEnvironment as updateEnvironmentAP<PERSON>,
  deleteEnvironment as deleteEnvironmentAPI,
} from '@/actions/environment';
import {
  EnvironmentActions,
  EnvironmentStates,
  EnvironmentType,
  CreateEnvironmentRequest,
  UpdateEnvironmentRequest,
} from '@/store/environment/type';

export const useEnvironmentStore = create<
  EnvironmentStates & EnvironmentActions
>(set => ({
  environments: [],
  loading: false,
  creating: false,
  updating: false,
  deleting: false,

  setEnvironments: (environments: EnvironmentType[]) =>
    set(() => ({ environments })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  setUpdating: (updating: boolean) => set(() => ({ updating })),
  setDeleting: (deleting: boolean) => set(() => ({ deleting })),

  createEnvironment: async (data: CreateEnvironmentRequest) => {
    try {
      set(() => ({ creating: true }));
      const response: any = await createEnvironmentAPI(data);
      set(() => ({ creating: false }));

      // Add the new environment to the list if creation was successful
      if (response?.status) {
        set(state => ({
          environments: [...state.environments, response.data],
        }));
      }

      return response;
    } catch (error) {
      console.error('Failed to create environment:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },

  updateEnvironment: async (id: number, data: UpdateEnvironmentRequest) => {
    try {
      set(() => ({ updating: true }));
      const response: any = await updateEnvironmentAPI(id, data);
      set(() => ({ updating: false }));

      // Update the environment in the list if the update was successful
      if (response?.status) {
        set(state => ({
          environments: state.environments.map(env =>
            env.id === id ? { ...env, ...response.data } : env
          ),
        }));
      }

      return response;
    } catch (error) {
      console.error('Failed to update environment:', error);
      set(() => ({ updating: false }));
      throw error;
    }
  },

  deleteEnvironment: async (id: number) => {
    try {
      set(() => ({ deleting: true }));
      const response: any = await deleteEnvironmentAPI(id);
      set(() => ({ deleting: false }));

      // Remove the environment from the list if the deletion was successful
      if (response?.status) {
        set(state => ({
          environments: state.environments.filter(env => env.id !== id),
        }));
      }

      return response;
    } catch (error) {
      console.error('Failed to delete environment:', error);
      set(() => ({ deleting: false }));
      throw error;
    }
  },

  addEnvironment: (environment: EnvironmentType) =>
    set(state => ({
      environments: [...state.environments, environment],
    })),

  removeEnvironment: (id: number) =>
    set(state => ({
      environments: state.environments.filter(env => env.id !== id),
    })),

  updateEnvironmentInStore: (id: number, environment: EnvironmentType) =>
    set(state => ({
      environments: state.environments.map(env =>
        env.id === id ? environment : env
      ),
    })),
}));
