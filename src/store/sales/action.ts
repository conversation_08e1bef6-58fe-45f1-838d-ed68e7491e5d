import { create } from 'zustand';

import { getSales, registerSale, deleteUser, updateUser } from '@/actions/user';
import {
  SalesActions,
  SalesStates,
  SaleUserType,
  GetSalesParams,
  CreateSaleRequest,
  UpdateSaleRequest,
} from '@/store/sales/type';

export const useSalesStore = create<SalesStates & SalesActions>(set => ({
  sales: [],
  loading: false,
  creating: false,
  deleting: false,
  updating: false,
  setSales: (sales: SaleUserType[]) => set(() => ({ sales })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  setDeleting: (deleting: boolean) => set(() => ({ deleting })),
  setUpdating: (updating: boolean) => set(() => ({ updating })),
  fetchSales: async (params?: GetSalesParams) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getSales(params);
      if (response?.status) {
        const sales = Array.isArray(response.data) ? response.data : [];
        set(() => ({ sales, loading: false }));
      } else {
        set(() => ({ sales: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch sales:', error);
      set(() => ({ sales: [], loading: false }));
    }
  },
  createSale: async (data: CreateSaleRequest) => {
    try {
      set(() => ({ creating: true }));
      const response: any = await registerSale(data);
      if (response?.status) {
        // Add the new sale to the existing sales list
        set(state => ({
          sales: [response.data, ...state.sales],
          creating: false,
        }));
        return response;
      } else {
        set(() => ({ creating: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to create sale:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },
  deleteSale: async (id: number) => {
    try {
      set(() => ({ deleting: true }));
      const response: any = await deleteUser(id);
      if (response?.status) {
        // Remove the deleted sale from the sales list
        set(state => ({
          sales: state.sales.filter(sale => sale.id !== id),
          deleting: false,
        }));
        return response;
      } else {
        set(() => ({ deleting: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to delete sale:', error);
      set(() => ({ deleting: false }));
      throw error;
    }
  },
  updateSale: async (id: number, data: UpdateSaleRequest) => {
    try {
      set(() => ({ updating: true }));
      const response: any = await updateUser(id, data);
      if (response?.status) {
        // Update the sale in the sales list
        set(state => ({
          sales: state.sales.map(sale =>
            sale.id === id ? response.data : sale
          ),
          updating: false,
        }));
        return response;
      } else {
        set(() => ({ updating: false }));
        return response;
      }
    } catch (error) {
      console.error('Failed to update sale:', error);
      set(() => ({ updating: false }));
      throw error;
    }
  },
}));
