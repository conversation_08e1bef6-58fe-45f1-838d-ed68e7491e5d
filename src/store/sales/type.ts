export interface SalesStates {
  sales: SaleUserType[];
  loading: boolean;
  creating: boolean;
  deleting: boolean;
  updating: boolean;
}

export interface SalesActions {
  setSales: (sales: SaleUserType[]) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  setUpdating: (updating: boolean) => void;
  fetchSales: (params?: GetSalesParams) => Promise<void>;
  createSale: (data: CreateSaleRequest) => Promise<any>;
  deleteSale: (id: number) => Promise<any>;
  updateSale: (id: number, data: UpdateSaleRequest) => Promise<any>;
}

export interface GetSalesParams {
  name?: string;
  email?: string;
}

export interface SaleUserType {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  email: string;
  user_type: {
    id: number;
    name: string;
    description: string;
    is_active: boolean;
    is_admin: boolean;
    is_member: boolean;
    is_sale: boolean;
  };
}

export interface GetSalesResponse {
  status: boolean;
  message: string;
  data: SaleUserType[];
}

export interface CreateSaleRequest {
  name: string;
  email: string;
  password: string;
}

export interface CreateSaleResponse {
  status: boolean;
  message: string;
  data: SaleUserType;
}

export interface UpdateSaleRequest {
  name: string;
  email: string;
}

export interface UpdateSaleResponse {
  status: boolean;
  message: string;
  data: SaleUserType;
}
