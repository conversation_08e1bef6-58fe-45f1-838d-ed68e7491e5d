export interface ClusterStates {
  clusters: ClusterType[];
  selectedCluster: DetailedClusterType | null;
  loading: boolean;
}

export interface ClusterActions {
  setClusters: (clusters: ClusterType[]) => void;
  setSelectedCluster: (cluster: DetailedClusterType | null) => void;
  setLoading: (loading: boolean) => void;
  fetchClusters: (workspace_id?: number) => Promise<void>;
  fetchCluster: (id: number) => Promise<void>;
  createCluster: (payload: {
    name: string;
    region: string;
    pool_name: string;
    size: string;
    node_count: number;
    workspace_id: number;
  }) => Promise<boolean>;
  updateCluster: (
    id: number,
    payload: {
      name: string;
      region: string;
      pool_name: string;
      size: string;
      node_count: number;
      workspace_id: number;
      status_id: number;
    }
  ) => Promise<boolean>;
}

export type StatusType = {
  id: number;
  name: string;
};

export type ClusterType = {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  region: string;
  pool_name: string;
  size: string;
  node_count: number;
  is_self: boolean;
  status?: {
    id: number;
    name: string;
  } | null;
  status_id?: number;
  workspace: {
    id: number;
    name: string;
    description: string;
  };
};

export type DetailedClusterType = {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  region: string;
  pool_name: string;
  size: string;
  node_count: number;
  is_self: boolean;
  workspace: {
    id: number;
    name: string;
    description: string;
  };
  status: StatusType;
  namespaces: NamespaceType[];
};

export type NamespaceType = {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
  deployments: DeploymentType[];
  services: ServiceType[];
  ingress: IngressType[];
};

export type DeploymentType = {
  id: number;
  name: string;
  image: string;
  container_port: number;
  replicas: number;
  status: StatusType;
  environments?: EnvironmentType[];
};

export type EnvironmentType = {
  id: number;
  name: string;
  value: string;
};

export type ServiceType = {
  id: number;
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  status: StatusType;
  ingress_specs?: IngressSpecType[];
};

export type IngressType = {
  id: number;
  name: string;
  class: string;
  status: StatusType;
  ingress_specs: IngressSpecType[];
};

export type IngressSpecType = {
  id: number;
  host: string;
  path: string;
  port: number;
  service?: ServiceType;
};
