import { create } from 'zustand';

import {
  getClusters,
  create<PERSON><PERSON> as create<PERSON><PERSON><PERSON><PERSON>,
  update<PERSON><PERSON> as update<PERSON><PERSON><PERSON><PERSON>,
  getCluster,
} from '@/actions/cluster';
import {
  ClusterActions,
  ClusterStates,
  ClusterType,
  DetailedClusterType,
} from '@/store/cluster/type';

export const useClusterStore = create<ClusterStates & ClusterActions>(set => ({
  clusters: [],
  selectedCluster: null,
  loading: false,
  setClusters: (clusters: ClusterType[]) => set(() => ({ clusters })),
  setSelectedCluster: (cluster: DetailedClusterType | null) =>
    set(() => ({ selectedCluster: cluster })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  fetchClusters: async (workspace_id?: number) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getClusters(workspace_id);
      if (response?.status) {
        set(() => ({ clusters: response.data, loading: false }));
      } else {
        set(() => ({ clusters: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch clusters:', error);
      set(() => ({ clusters: [], loading: false }));
    }
  },
  fetchCluster: async (id: number) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getCluster(id);
      if (response?.status) {
        set(() => ({ selectedCluster: response.data, loading: false }));
      } else {
        set(() => ({ selectedCluster: null, loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch cluster:', error);
      set(() => ({ selectedCluster: null, loading: false }));
    }
  },
  createCluster: async (payload: {
    name: string;
    region: string;
    pool_name: string;
    size: string;
    node_count: number;
    workspace_id: number;
  }) => {
    try {
      const response: any = await createClusterApi(payload);
      if (response?.status) {
        // Refresh clusters after successful creation with the same workspace_id
        const clustersResponse: any = await getClusters(payload.workspace_id);
        if (clustersResponse?.status) {
          set(() => ({ clusters: clustersResponse.data }));
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to create cluster:', error);
      return false;
    }
  },
  updateCluster: async (
    id: number,
    payload: {
      name: string;
      region: string;
      pool_name: string;
      size: string;
      node_count: number;
      workspace_id: number;
      status_id: number;
    }
  ) => {
    try {
      const response: any = await updateClusterApi(id, payload);
      if (response?.status) {
        // Refresh clusters after successful update with the same workspace_id
        const clustersResponse: any = await getClusters(payload.workspace_id);
        if (clustersResponse?.status) {
          set(() => ({ clusters: clustersResponse.data }));
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to update cluster:', error);
      return false;
    }
  },
}));
