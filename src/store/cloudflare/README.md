# Cloudflare Store

This store manages Cloudflare zones data fetched from the `/cloudflare/zones` API endpoint.

## Features

- Fetch Cloudflare zones with DNS records
- Loading state management
- Error handling
- TypeScript support

## Usage

### Basic Usage

```typescript
import { useCloudflareStore } from '@/store/cloudflare/action';

function CloudflareZones() {
  const { zones, loading, error, fetchCloudflareZones, clearError } = useCloudflareStore();

  useEffect(() => {
    fetchCloudflareZones();
  }, []);

  if (loading) return <div>Loading Cloudflare zones...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!zones) return <div>No zones data available</div>;

  return (
    <div>
      <h2>Cloudflare Zones ({zones.data.summary.total_domains} domains)</h2>
      {zones.data.zones_with_dns_records.map(zone => (
        <div key={zone.zone_id}>
          <h3>{zone.zone_name}</h3>
          <p>Status: {zone.zone_status}</p>
          <p>Account: {zone.account.name}</p>
          <p>DNS Records: {zone.dns_records.length}</p>
        </div>
      ))}
    </div>
  );
}
```

### Error Handling

```typescript
function CloudflareZonesWithErrorHandling() {
  const { zones, loading, error, fetchCloudflareZones, clearError } = useCloudflareStore();

  const handleRetry = () => {
    clearError();
    fetchCloudflareZones();
  };

  if (error) {
    return (
      <div>
        <p>Error: {error}</p>
        <button onClick={handleRetry}>Retry</button>
      </div>
    );
  }

  // ... rest of component
}
```

## API Response Structure

The API returns data in the following format:

```typescript
{
  status: boolean,
  message: string,
  data: {
    summary: {
      total_domains: number,
      zones_with_record_counts: {
        [domain_name: string]: number
      }
    },
    zones_with_dns_records: [
      {
        zone_id: string,
        zone_name: string,
        zone_status: string,
        account: {
          id: string,
          name: string
        },
        dns_records: [
          {
            id: string,
            name: string,
            type: string,
            content: string,
            proxied: boolean
          }
        ]
      }
    ]
  }
}
```

## Store State

- `zones`: The complete API response data or null
- `loading`: Boolean indicating if a request is in progress
- `error`: Error message string or null

## Store Actions

- `fetchCloudflareZones()`: Fetch zones data from the API
- `setZones(zones)`: Manually set zones data
- `setLoading(loading)`: Set loading state
- `setError(error)`: Set error message
- `clearError()`: Clear the current error
