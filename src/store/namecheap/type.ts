export interface PriceInfo {
  Duration: string;
  DurationType: string;
  Price: string;
  PricingType: string;
  AdditionalCost: string;
  RegularPrice: string;
  RegularPriceType: string;
  RegularAdditionalCost: string;
  RegularAdditionalCostType: string;
  YourPrice: string;
  YourPriceType: string;
  YourAdditonalCost: string;
  YourAdditonalCostType: string;
  PromotionPrice: string;
  Currency: string;
}

export interface DomainCheckResult {
  Domain: string;
  Available: string;
  ErrorNo: string;
  Description: string;
  IsPremiumName: string;
  PremiumRegistrationPrice: string;
  PremiumRenewalPrice: string;
  PremiumRestorePrice: string;
  PremiumTransferPrice: string;
  IcannFee: string;
  EapFee: string;
  Price: PriceInfo[];
}

export interface NamecheapDomainCheckRequest {
  domains: string[];
}

export interface NamecheapDomainCheckResponse {
  status: boolean;
  message: string;
  data: {
    DomainCheckResult: DomainCheckResult[];
  };
}

export interface NamecheapStates {
  domainCheckResults: DomainCheckResult[];
  loading: boolean;
  error: string | null;
}

export interface NamecheapActions {
  setDomainCheckResults: (results: DomainCheckResult[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  checkDomains: (
    domains: string[]
  ) => Promise<NamecheapDomainCheckResponse | null>;
  clearResults: () => void;
}
