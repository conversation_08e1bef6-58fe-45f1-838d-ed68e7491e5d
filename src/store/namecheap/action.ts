import { create } from 'zustand';

import { checkDomains as checkDomainsAPI } from '@/actions/namecheap';
import {
  NamecheapActions,
  NamecheapStates,
  DomainCheckResult,
  NamecheapDomainCheckResponse,
} from '@/store/namecheap/type';

export const useNamecheapStore = create<NamecheapStates & NamecheapActions>(
  set => ({
    domainCheckResults: [],
    loading: false,
    error: null,

    setDomainCheckResults: (results: DomainCheckResult[]) =>
      set(() => ({ domainCheckResults: results })),
    setLoading: (loading: boolean) => set(() => ({ loading })),
    setError: (error: string | null) => set(() => ({ error })),
    clearError: () => set(() => ({ error: null })),
    clearResults: () => set(() => ({ domainCheckResults: [] })),

    checkDomains: async (
      domains: string[]
    ): Promise<NamecheapDomainCheckResponse | null> => {
      try {
        set(() => ({ loading: true, error: null }));
        const response = await checkDomainsAPI({ domains });

        if (response?.status) {
          set(() => ({
            domainCheckResults: response.data.DomainCheckResult,
            loading: false,
          }));
          return response;
        } else {
          set(() => ({
            domainCheckResults: [],
            loading: false,
            error: response?.message || 'Domain check failed',
          }));
          return null;
        }
      } catch (error) {
        console.error('Failed to check domains:', error);
        set(() => ({
          domainCheckResults: [],
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : 'An error occurred while checking domains',
        }));
        return null;
      }
    },
  })
);
