import { create } from 'zustand';

import {
  createWorkspace as createWorks<PERSON><PERSON><PERSON>,
  getWorkspaces,
} from '@/actions/workspace';
import {
  WorkspaceActions,
  WorkspaceStates,
  WorkspaceType,
} from '@/store/workspace/type';

export const useWorkspaceStore = create<WorkspaceStates & WorkspaceActions>(
  set => ({
    workspaces: [],
    selectedWorkspace: null,
    loading: false,
    setWorkspaces: (workspaces: WorkspaceType[]) => set(() => ({ workspaces })),
    setSelectedWorkspace: (workspace: WorkspaceType | null) =>
      set(() => ({ selectedWorkspace: workspace })),
    setLoading: (loading: boolean) => set(() => ({ loading })),
    fetchWorkspaces: async () => {
      try {
        set(() => ({ loading: true }));
        const response: any = await getWorkspaces();
        if (response?.status) {
          const workspaces = response.data || [];
          set(state => ({
            workspaces,
            loading: false,
            // Auto-select first workspace if no workspace is currently selected
            selectedWorkspace:
              state.selectedWorkspace ||
              (Array.isArray(workspaces) && workspaces.length > 0
                ? workspaces[0]
                : null),
          }));
        } else {
          set(() => ({
            workspaces: [],
            selectedWorkspace: null,
            loading: false,
          }));
        }
      } catch (error) {
        console.error('Failed to fetch workspaces:', error);
        set(() => ({
          workspaces: [],
          selectedWorkspace: null,
          loading: false,
        }));
      }
    },
    createWorkspace: async (payload: { name: string; description: string }) => {
      try {
        const response: any = await createWorkspaceApi(payload);
        if (response?.status) {
          // Refresh workspaces after successful creation
          const workspacesResponse: any = await getWorkspaces();
          if (workspacesResponse?.status) {
            const workspaces = workspacesResponse.data || [];
            const newWorkspace = workspaces.find(
              (w: WorkspaceType) => w.name === payload.name
            );
            set(() => ({
              workspaces,
              selectedWorkspace:
                newWorkspace ||
                (Array.isArray(workspaces) && workspaces.length > 0
                  ? workspaces[0]
                  : null),
            }));
          }
          return true;
        }
        return false;
      } catch (error) {
        console.error('Failed to create workspace:', error);
        return false;
      }
    },
  })
);
