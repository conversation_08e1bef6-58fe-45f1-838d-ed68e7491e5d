export interface WorkspaceStates {
  workspaces: WorkspaceType[];
  selectedWorkspace: WorkspaceType | null;
  loading: boolean;
}

export interface WorkspaceActions {
  setWorkspaces: (workspaces: WorkspaceType[]) => void;
  setSelectedWorkspace: (workspace: WorkspaceType | null) => void;
  setLoading: (loading: boolean) => void;
  fetchWorkspaces: () => Promise<void>;
  createWorkspace: (payload: {
    name: string;
    description: string;
  }) => Promise<boolean>;
}

export type WorkspaceType = {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  user: {
    id: number;
    name: string;
    email: string;
  };
};
