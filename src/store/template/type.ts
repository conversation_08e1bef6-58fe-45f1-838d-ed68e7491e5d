export interface TemplateStates {
  templates: TemplateType[];
  selectedTemplate: TemplateType | null;
  loading: boolean;
}

export interface TemplateActions {
  setTemplates: (templates: TemplateType[]) => void;
  setSelectedTemplate: (template: TemplateType | null) => void;
  setLoading: (loading: boolean) => void;
  fetchTemplates: () => Promise<void>;
}

export interface TemplateStatusType {
  id: number;
  name: string;
}

export interface TemplateClusterType {
  id: number;
  name: string;
  region: string;
  pool_name: string;
  size: string;
  node_count: number;
  load_balance_ip: string;
  status: TemplateStatusType;
}

export interface TemplateType {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
  cluster: TemplateClusterType;
}

export interface TemplateApiResponse {
  status: boolean;
  message: string;
  data: TemplateType[];
}
