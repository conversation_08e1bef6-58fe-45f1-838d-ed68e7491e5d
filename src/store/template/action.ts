import { create } from 'zustand';

import { getTemplates } from '@/actions/template';
import {
  TemplateActions,
  TemplateStates,
  TemplateType,
} from '@/store/template/type';

export const useTemplateStore = create<TemplateStates & TemplateActions>(
  set => ({
    templates: [],
    selectedTemplate: null,
    loading: false,

    setTemplates: (templates: TemplateType[]) => set(() => ({ templates })),
    setSelectedTemplate: (template: TemplateType | null) =>
      set(() => ({ selectedTemplate: template })),
    setLoading: (loading: boolean) => set(() => ({ loading })),

    fetchTemplates: async () => {
      try {
        set(() => ({ loading: true }));
        const response: any = await getTemplates();
        if (response?.status) {
          const templates = Array.isArray(response.data) ? response.data : [];
          set(() => ({ templates, loading: false }));
        } else {
          set(() => ({ templates: [], loading: false }));
        }
      } catch (error) {
        console.error('Failed to fetch templates:', error);
        set(() => ({ templates: [], loading: false }));
      }
    },
  })
);
