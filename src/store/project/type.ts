export interface ProjectStates {
  projects: ProjectType[];
  selectedProject: ProjectType | null;
  loading: boolean;
  creating: boolean;
  creatingFromTemplate: boolean;
  creatingFromOrder: boolean;
  updatingActive: boolean;
  deleting: boolean;
}

export interface ProjectActions {
  setProjects: (projects: ProjectType[]) => void;
  setSelectedProject: (project: ProjectType | null) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setCreatingFromTemplate: (creatingFromTemplate: boolean) => void;
  setCreatingFromOrder: (creatingFromOrder: boolean) => void;
  setUpdatingActive: (updatingActive: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  fetchProjects: (filters?: {
    type?: string;
    is_active?: boolean;
    cluster_id?: number;
    workspace_id?: number;
    name?: string;
  }) => Promise<void>;
  fetchProject: (id: number) => Promise<void>;
  createProject: (data: CreateProjectRequest) => Promise<any>;
  createProjectFromTemplate: (
    data: CreateProjectFromTemplateRequest
  ) => Promise<any>;
  createProjectFromOrder: (data: CreateProjectFromOrderRequest) => Promise<any>;
  updateProjectActive: (
    id: number,
    data: UpdateProjectActiveRequest
  ) => Promise<any>;
  deleteProject: (id: number) => Promise<any>;
}

export interface CreateProjectRequest {
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
  cluster_id: number;
  deployments: CreateDeploymentRequest[];
}

export interface CreateProjectFromTemplateRequest {
  name: string;
  template_id: number;
}

export interface CreateProjectFromOrderRequest {
  order_id: number;
}

export interface UpdateProjectActiveRequest {
  is_active: boolean;
}

export interface CreateDeploymentRequest {
  name: string;
  image: string;
  container_port: number;
  replicas: number;
  environments: CreateEnvironmentRequest[];
  services: CreateServiceRequest[];
}

export interface CreateEnvironmentRequest {
  name: string;
  value: string;
}

export interface CreateServiceRequest {
  name: string;
  port: string;
  target_port: string;
  type: string;
}

export type StatusType = {
  id: number;
  name: string;
};

export type EnvironmentType = {
  id: number;
  name: string;
  value: string;
  deployment_id: number;
  created_at?: string;
  updated_at?: string;
};

export type DeploymentType = {
  id: number;
  name: string;
  image: string;
  container_port: number;
  replicas: number;
  status: StatusType;
  environments?: EnvironmentType[];
};

export type IngressSpecType = {
  id: number;
  host: string;
  path: string;
  port: number;
  service?: {
    id: number;
    name: string;
    port: string;
    target_port: string;
    type: string;
    cluster_ip?: string;
    external_ip?: string;
  };
};

export type ServiceType = {
  id: number;
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  status: StatusType;
  ingress_specs: IngressSpecType[];
};

export type IngressType = {
  id: number;
  name: string;
  class: string;
  status: StatusType;
  ingress_specs: IngressSpecType[];
};

export type ClusterInfo = {
  id: number;
  name: string;
  region: string;
  pool_name: string;
  size: string;
  node_count: number;
  status: StatusType;
};

export type ProjectType = {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
  cluster: ClusterInfo;
  deployments: DeploymentType[];
  services: ServiceType[];
  ingress: IngressType[];
  deployment_count: number;
  service_count: number;
  ingress_count: number;
};
