import { create } from 'zustand';

import { getDashboard } from '@/actions/dashboard';
import { DashboardActions, DashboardStates } from '@/store/dashboard/type';

export const useDashboardStore = create<DashboardStates & DashboardActions>(
  set => ({
    salesAmount: 0,
    websAmount: 0,
    loading: false,
    setSalesAmount: (amount: number) => set(() => ({ salesAmount: amount })),
    setWebsAmount: (amount: number) => set(() => ({ websAmount: amount })),
    setLoading: (loading: boolean) => set(() => ({ loading })),
    fetchDashboard: async () => {
      try {
        set(() => ({ loading: true }));
        const response: any = await getDashboard();
        if (response?.status) {
          set(() => ({
            salesAmount: response.data.sales_amount,
            websAmount: response.data.webs_amount,
            loading: false,
          }));
        } else {
          set(() => ({ loading: false }));
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        set(() => ({ loading: false }));
      }
    },
  })
);
