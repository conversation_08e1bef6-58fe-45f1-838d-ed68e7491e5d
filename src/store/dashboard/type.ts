export interface DashboardStates {
  salesAmount: number;
  websAmount: number;
  loading: boolean;
}

export interface DashboardActions {
  setSalesAmount: (amount: number) => void;
  setWebsAmount: (amount: number) => void;
  setLoading: (loading: boolean) => void;
  fetchDashboard: () => Promise<void>;
}

export interface DashboardData {
  sales_amount: number;
  webs_amount: number;
}

export interface DashboardResponse {
  status: boolean;
  message: string;
  data: DashboardData;
}
