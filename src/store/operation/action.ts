import { create } from 'zustand';

import {
  createClusterOperation as createClusterOperation<PERSON><PERSON>,
  createOperation as createOperation<PERSON><PERSON>,
} from '@/actions/operation';
import {
  OperationActions,
  OperationStates,
  OperationType,
  CreateOperationResponse,
} from '@/store/operation/type';

export const useOperationStore = create<OperationStates & OperationActions>(
  set => ({
    loading: false,
    confirmLoading: false,
    lastOperation: null,
    setLoading: (loading: boolean) => set(() => ({ loading })),
    setConfirmLoading: (confirmLoading: boolean) =>
      set(() => ({ confirmLoading })),
    setLastOperation: (operation: OperationType | null) =>
      set(() => ({ lastOperation: operation })),
    createClusterOperation: async (payload: {
      cluster_id: number;
      method: string;
    }) => {
      try {
        set(() => ({ loading: true }));
        const response: any = await createClusterOperationApi(payload);
        if (response?.status) {
          set(() => ({
            lastOperation: response.data,
            loading: false,
          }));
          return true;
        } else {
          set(() => ({ loading: false }));
          return false;
        }
      } catch (error) {
        console.error('Failed to create cluster operation:', error);
        set(() => ({ loading: false }));
        return false;
      }
    },
    createOperation: async (payload: {
      cluster_id: number;
      namespace_id: number;
      method: string;
    }) => {
      try {
        set(() => ({ loading: true }));
        const response: CreateOperationResponse =
          await createOperationApi(payload);
        if (response?.status) {
          set(() => ({
            loading: false,
          }));
          return true;
        } else {
          set(() => ({ loading: false }));
          return false;
        }
      } catch (error) {
        console.error('Failed to create operation:', error);
        set(() => ({ loading: false }));
        return false;
      }
    },
  })
);
