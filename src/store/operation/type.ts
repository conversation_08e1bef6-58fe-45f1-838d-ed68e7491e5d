export interface OperationStates {
  loading: boolean;
  confirmLoading: boolean;
  lastOperation: OperationType | null;
}

export interface OperationActions {
  setLoading: (loading: boolean) => void;
  setConfirmLoading: (loading: boolean) => void;
  setLastOperation: (operation: OperationType | null) => void;
  createClusterOperation: (payload: {
    cluster_id: number;
    method: string;
  }) => Promise<boolean>;
  createOperation: (payload: {
    cluster_id: number;
    namespace_id: number;
    method: string;
  }) => Promise<boolean>;
}

export interface OperationType {
  id: number;
  cluster_id: number;
  method: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateOperationResponse {
  status: boolean;
  message: string;
  data?: string;
}
