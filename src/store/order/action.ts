import { create } from 'zustand';

import {
  getOrders,
  getMyOrders,
  getOrder,
  createOrder,
  createOrderDomain,
  updateOrderDomainAvailability,
  deleteOrderDomain,
} from '@/actions/order';
import {
  OrderActions,
  OrderStates,
  DetailedOrderType,
  OrderQueryParams,
  MyOrderQueryParams,
  CreateOrderRequest,
  CreateOrderDomainRequest,
  UpdateOrderDomainAvailabilityRequest,
} from '@/store/order/type';

export const useOrderStore = create<OrderStates & OrderActions>(set => ({
  orders: [],
  myOrders: [],
  selectedOrder: null,
  loading: false,
  myOrdersLoading: false,
  orderLoading: false,
  creating: false,
  creatingOrderDomain: false,
  updatingOrderDomainAvailability: false,
  deletingOrderDomain: false,

  setOrders: (orders: DetailedOrderType[]) => set(() => ({ orders })),
  setMyOrders: (orders: DetailedOrderType[]) =>
    set(() => ({ myOrders: orders })),
  setSelectedOrder: (order: DetailedOrderType | null) =>
    set(() => ({ selectedOrder: order })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setMyOrdersLoading: (loading: boolean) =>
    set(() => ({ myOrdersLoading: loading })),
  setOrderLoading: (loading: boolean) => set(() => ({ orderLoading: loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  setCreatingOrderDomain: (creating: boolean) =>
    set(() => ({ creatingOrderDomain: creating })),
  setUpdatingOrderDomainAvailability: (updating: boolean) =>
    set(() => ({ updatingOrderDomainAvailability: updating })),
  setDeletingOrderDomain: (deleting: boolean) =>
    set(() => ({ deletingOrderDomain: deleting })),

  fetchOrders: async (params?: OrderQueryParams) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getOrders(params);
      if (response?.status) {
        const orders = Array.isArray(response.data) ? response.data : [];
        set(() => ({ orders, loading: false }));
      } else {
        set(() => ({ orders: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      set(() => ({ orders: [], loading: false }));
    }
  },

  fetchMyOrders: async (params?: MyOrderQueryParams) => {
    try {
      set(() => ({ myOrdersLoading: true }));
      const response: any = await getMyOrders(params);
      if (response?.status) {
        const myOrders = Array.isArray(response.data) ? response.data : [];
        set(() => ({ myOrders, myOrdersLoading: false }));
      } else {
        set(() => ({ myOrders: [], myOrdersLoading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch my orders:', error);
      set(() => ({ myOrders: [], myOrdersLoading: false }));
    }
  },

  fetchOrder: async (id: number) => {
    try {
      set(() => ({ orderLoading: true }));
      const response: any = await getOrder(id);
      if (response?.status) {
        set(() => ({ selectedOrder: response.data, orderLoading: false }));
      } else {
        set(() => ({ selectedOrder: null, orderLoading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch order:', error);
      set(() => ({ selectedOrder: null, orderLoading: false }));
    }
  },

  createOrder: async (data: CreateOrderRequest) => {
    try {
      set(() => ({ creating: true }));
      const response: any = await createOrder(data);
      set(() => ({ creating: false }));
      return response;
    } catch (error) {
      console.error('Failed to create order:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },

  createOrderDomain: async (data: CreateOrderDomainRequest) => {
    try {
      set(() => ({ creatingOrderDomain: true }));
      const response: any = await createOrderDomain(data);
      set(() => ({ creatingOrderDomain: false }));
      return response;
    } catch (error) {
      console.error('Failed to create order domain:', error);
      set(() => ({ creatingOrderDomain: false }));
      throw error;
    }
  },

  updateOrderDomainAvailability: async (
    id: number,
    data: UpdateOrderDomainAvailabilityRequest
  ) => {
    try {
      set(() => ({ updatingOrderDomainAvailability: true }));
      const response: any = await updateOrderDomainAvailability(id, data);
      set(() => ({ updatingOrderDomainAvailability: false }));
      return response;
    } catch (error) {
      console.error('Failed to update order domain availability:', error);
      set(() => ({ updatingOrderDomainAvailability: false }));
      throw error;
    }
  },

  deleteOrderDomain: async (id: number) => {
    try {
      set(() => ({ deletingOrderDomain: true }));
      const response: any = await deleteOrderDomain(id);
      set(() => ({ deletingOrderDomain: false }));
      return response;
    } catch (error) {
      console.error('Failed to delete order domain:', error);
      set(() => ({ deletingOrderDomain: false }));
      throw error;
    }
  },
}));
