import { create } from 'zustand';

import { getNamespaceTypes } from '@/actions/namespace';
import { NamespaceActions, NamespaceStates } from '@/store/namespace/type';

export const useNamespaceStore = create<NamespaceStates & NamespaceActions>(
  set => ({
    types: [],
    loading: false,
    setTypes: (types: string[]) => set(() => ({ types })),
    setLoading: (loading: boolean) => set(() => ({ loading })),
    fetchTypes: async () => {
      try {
        set(() => ({ loading: true }));
        const response: any = await getNamespaceTypes();
        if (response?.status) {
          set(() => ({ types: response.data, loading: false }));
        } else {
          set(() => ({ types: [], loading: false }));
        }
      } catch (error) {
        console.error('Failed to fetch namespace types:', error);
        set(() => ({ types: [], loading: false }));
      }
    },
  })
);
