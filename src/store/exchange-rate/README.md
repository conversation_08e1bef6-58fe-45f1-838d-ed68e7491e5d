# Exchange Rate Store

This store manages USD to THB exchange rate data fetched from the external Exchange Rate API (https://api.exchangerate-api.com/v4/latest/USD).

## Features

- Fetch USD to THB exchange rate from external API
- Store only THB rate (filters out other currencies)
- Loading state management
- Error handling
- Last updated timestamp tracking
- TypeScript support

## Usage

### Basic Usage

```typescript
import { useExchangeRateStore } from '@/store/exchange-rate/action';

function MyComponent() {
  const {
    thbRate,
    loading,
    error,
    lastUpdated,
    fetchTHBRate,
    clearError,
  } = useExchangeRateStore();

  // Fetch THB rate
  const handleFetchRate = async () => {
    const rate = await fetchTHBRate();
    if (rate) {
      console.log('THB Rate:', rate.thb_rate);
    }
  };

  return (
    <div>
      <button onClick={handleFetchRate} disabled={loading}>
        {loading ? 'Loading...' : 'Get THB Rate'}
      </button>
      
      {error && <div>Error: {error}</div>}
      
      {thbRate && (
        <div>
          <p>1 USD = {thbRate.thb_rate} THB</p>
          <p>Date: {thbRate.date}</p>
          <p>Last Updated: {lastUpdated?.toLocaleString()}</p>
        </div>
      )}
    </div>
  );
}
```

### Using the THBRateDisplay Component

```typescript
import THBRateDisplay from '@/components/exchange-rate/THBRateDisplay';

function MyPage() {
  return (
    <div>
      {/* Auto-fetch rate on component mount */}
      <THBRateDisplay autoFetch={true} />
      
      {/* Manual fetch only */}
      <THBRateDisplay autoFetch={false} showLastUpdated={true} />
    </div>
  );
}
```

## Store Methods

### State Properties

- `thbRate: THBRateResponse | null` - Current THB exchange rate data
- `loading: boolean` - Loading state
- `error: string | null` - Error message if any
- `lastUpdated: Date | null` - Timestamp of last successful fetch

### Actions

- `fetchTHBRate()` - Fetch current USD to THB exchange rate
- `setTHBRate(rate)` - Set the THB rate data
- `setLoading(loading)` - Set loading state
- `setError(error)` - Set error message
- `clearError()` - Clear error message
- `clearRate()` - Clear rate data and last updated timestamp

## API Response Structure

The external API returns comprehensive exchange rate data, but we filter and store only the THB rate:

### External API Response (Full)
```typescript
{
  provider: "https://www.exchangerate-api.com",
  WARNING_UPGRADE_TO_V6: "https://www.exchangerate-api.com/docs/free",
  terms: "https://www.exchangerate-api.com/terms",
  base: "USD",
  date: "2025-09-13",
  time_last_updated: **********,
  rates: {
    USD: 1,
    THB: 31.74,
    // ... other currencies
  }
}
```

### Stored Data (THB Only)
```typescript
{
  base: "USD",
  date: "2025-09-13",
  thb_rate: 31.74,
  time_last_updated: **********
}
```

## Error Handling

The store includes comprehensive error handling:

- Network errors
- API response errors
- Missing THB rate in response
- Timeout errors

Errors are stored in the `error` state and can be cleared using `clearError()`.

## Implementation Notes

- Uses a separate `HttpExternal` client for external API calls
- Server-side action for API calls (`getTHBExchangeRate`)
- Zustand store for state management
- TypeScript interfaces for type safety
- Only stores THB rate to minimize data storage and improve performance
