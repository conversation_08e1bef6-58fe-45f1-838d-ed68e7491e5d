// Exchange Rate API Response Types
export interface ExchangeRateApiResponse {
  provider: string;
  WARNING_UPGRADE_TO_V6: string;
  terms: string;
  base: string;
  date: string;
  time_last_updated: number;
  rates: {
    [currency: string]: number;
  };
}

// Simplified response for THB rate only
export interface THBRateResponse {
  base: string;
  date: string;
  thb_rate: number;
  time_last_updated: number;
}

// Store State and Actions
export interface ExchangeRateStates {
  thbRate: THBRateResponse | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface ExchangeRateActions {
  setTHBRate: (rate: THBRateResponse | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLastUpdated: (date: Date | null) => void;
  fetchTHBRate: () => Promise<THBRateResponse | null>;
  clearError: () => void;
  clearRate: () => void;
}
