import { create } from 'zustand';

import { getTHBExchangeRate } from '@/actions/exchange-rate';
import {
  ExchangeRateActions,
  ExchangeRateStates,
  THBRateResponse,
} from '@/store/exchange-rate/type';

export const useExchangeRateStore = create<
  ExchangeRateStates & ExchangeRateActions
>(set => ({
  thbRate: null,
  loading: false,
  error: null,
  lastUpdated: null,

  setTHBRate: (rate: THBRateResponse | null) => set(() => ({ thbRate: rate })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setError: (error: string | null) => set(() => ({ error })),
  setLastUpdated: (date: Date | null) => set(() => ({ lastUpdated: date })),
  clearError: () => set(() => ({ error: null })),
  clearRate: () => set(() => ({ thbRate: null, lastUpdated: null })),

  fetchTHBRate: async (): Promise<THBRateResponse | null> => {
    try {
      set(() => ({ loading: true, error: null }));
      const response = await getTHBExchangeRate();

      set(() => ({
        thbRate: response,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      }));

      return response;
    } catch (error) {
      console.error('Failed to fetch THB exchange rate:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'An error occurred while fetching THB exchange rate';

      set(() => ({
        thbRate: null,
        loading: false,
        error: errorMessage,
        lastUpdated: null,
      }));

      return null;
    }
  },
}));
