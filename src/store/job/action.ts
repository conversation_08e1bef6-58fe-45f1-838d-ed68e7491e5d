import { create } from 'zustand';

import { getJobs } from '@/actions/job';
import {
  JobActions,
  JobStates,
  JobType,
  JobQueryParams,
} from '@/store/job/type';

export const useJobStore = create<JobStates & JobActions>(set => ({
  jobs: [],
  selectedJob: null,
  loading: false,
  setJobs: (jobs: JobType[]) => set(() => ({ jobs })),
  setSelectedJob: (job: JobType | null) => set(() => ({ selectedJob: job })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  fetchJobs: async (params?: JobQueryParams) => {
    try {
      set(() => ({ loading: true }));
      const response: any = await getJobs(params);
      if (response?.status) {
        const jobs =
          Array.isArray(response.data) && response.data.length > 0
            ? response.data
            : [];
        set(() => ({ jobs, loading: false }));
      } else {
        set(() => ({ jobs: [], loading: false }));
      }
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      set(() => ({ jobs: [], loading: false }));
    }
  },
}));
