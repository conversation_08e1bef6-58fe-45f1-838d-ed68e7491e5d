export interface JobStates {
  jobs: JobType[];
  selectedJob: JobType | null;
  loading: boolean;
}

export interface JobActions {
  setJobs: (jobs: JobType[]) => void;
  setSelectedJob: (job: JobType | null) => void;
  setLoading: (loading: boolean) => void;
  fetchJobs: (params?: JobQueryParams) => Promise<void>;
}

export interface JobQueryParams {
  my_jobs?: boolean;
  name?: string;
  job_status_id?: number;
  event_id?: number;
  event?: string;
  action?: string;
}

export interface JobStatusType {
  id: number;
  name: string;
}

export interface UserType {
  id: number;
  name: string;
  email: string;
}

export interface JobLogType {
  id: number;
  name: string;
  description: string;
}

export interface JobType {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  description: string;
  event_id: number;
  event: string;
  action: string;
  job_status: JobStatusType;
  user: UserType;
  job_logs: JobLogType[];
}
