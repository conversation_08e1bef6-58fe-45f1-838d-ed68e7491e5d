import { create } from 'zustand';

import {
  getIngressSpecs,
  getIngressSpec,
  createIngressSpec as createIngressSpecAPI,
  updateIngressSpec as updateIngressSpecAPI,
  deleteIngressSpec as deleteIngressSpecAPI,
} from '@/actions/ingress-spec';
import {
  IngressSpecActions,
  IngressSpecStates,
  IngressSpecType,
  CreateIngressSpecRequest,
  UpdateIngressSpecRequest,
  IngressSpecFilters,
} from '@/store/ingress-spec/type';

export const useIngressSpecStore = create<
  IngressSpecStates & IngressSpecActions
>((set, get) => ({
  ingressSpecs: [],
  selectedIngressSpec: null,
  loading: false,
  creating: false,
  updating: false,
  deleting: false,

  setIngressSpecs: (ingressSpecs: IngressSpecType[]) =>
    set(() => ({ ingressSpecs })),
  setSelectedIngressSpec: (ingressSpec: IngressSpecType | null) =>
    set(() => ({ selectedIngressSpec: ingressSpec })),
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setCreating: (creating: boolean) => set(() => ({ creating })),
  setUpdating: (updating: boolean) => set(() => ({ updating })),
  setDeleting: (deleting: boolean) => set(() => ({ deleting })),

  fetchIngressSpecs: async (filters?: IngressSpecFilters) => {
    set(() => ({ loading: true }));
    try {
      const response: any = await getIngressSpecs(filters);
      set(() => ({ ingressSpecs: response.data || [], loading: false }));
    } catch (error) {
      console.error('Error fetching ingress specs:', error);
      set(() => ({ loading: false }));
      throw error;
    }
  },

  fetchIngressSpec: async (id: number) => {
    set(() => ({ loading: true }));
    try {
      const response: any = await getIngressSpec(id);
      set(() => ({ selectedIngressSpec: response.data, loading: false }));
    } catch (error) {
      console.error('Error fetching ingress spec:', error);
      set(() => ({ loading: false }));
      throw error;
    }
  },

  createIngressSpec: async (data: CreateIngressSpecRequest) => {
    set(() => ({ creating: true }));
    try {
      const response = await createIngressSpecAPI(data);

      // Refresh the ingress specs list
      const { fetchIngressSpecs } = get();
      await fetchIngressSpecs();

      set(() => ({ creating: false }));
      return response;
    } catch (error) {
      console.error('Error creating ingress spec:', error);
      set(() => ({ creating: false }));
      throw error;
    }
  },

  updateIngressSpec: async (id: number, data: UpdateIngressSpecRequest) => {
    set(() => ({ updating: true }));
    try {
      const response = await updateIngressSpecAPI(id, data);

      // Refresh the ingress specs list
      const { fetchIngressSpecs } = get();
      await fetchIngressSpecs();

      set(() => ({ updating: false }));
      return response;
    } catch (error) {
      console.error('Error updating ingress spec:', error);
      set(() => ({ updating: false }));
      throw error;
    }
  },

  deleteIngressSpec: async (id: number) => {
    set(() => ({ deleting: true }));
    try {
      const response = await deleteIngressSpecAPI(id);

      // Refresh the ingress specs list
      const { fetchIngressSpecs } = get();
      await fetchIngressSpecs();

      set(() => ({ deleting: false }));
      return response;
    } catch (error) {
      console.error('Error deleting ingress spec:', error);
      set(() => ({ deleting: false }));
      throw error;
    }
  },
}));
