export interface IngressSpecType {
  id: number;
  created_at: string;
  updated_at: string;
  host: string;
  path: string;
  port: number;
  service_id: number;
  ingress_id: number;
  service?: ServiceType;
  ingress?: IngressType;
}

export interface ServiceType {
  id: number;
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
  status?: StatusType;
}

export interface IngressType {
  id: number;
  name: string;
  class: string;
  namespace_id: number;
  status?: StatusType;
}

export interface StatusType {
  id: number;
  name: string;
}

export interface CreateIngressSpecRequest {
  host: string;
  path: string;
  port: number;
  service_id: number;
  ingress_id: number;
}

export interface UpdateIngressSpecRequest {
  host: string;
  path: string;
  port: number;
}

export interface IngressSpecFilters {
  ingress_id?: number;
  service_id?: number;
  host?: string;
  path?: string;
}

export interface IngressSpecStates {
  ingressSpecs: IngressSpecType[];
  selectedIngressSpec: IngressSpecType | null;
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
}

export interface IngressSpecActions {
  setIngressSpecs: (ingressSpecs: IngressSpecType[]) => void;
  setSelectedIngressSpec: (ingressSpec: IngressSpecType | null) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  fetchIngressSpecs: (filters?: IngressSpecFilters) => Promise<void>;
  fetchIngressSpec: (id: number) => Promise<void>;
  createIngressSpec: (data: CreateIngressSpecRequest) => Promise<any>;
  updateIngressSpec: (
    id: number,
    data: UpdateIngressSpecRequest
  ) => Promise<any>;
  deleteIngressSpec: (id: number) => Promise<any>;
}
