import { NextRequest, NextResponse } from 'next/server';

export function middleware(req: NextRequest) {
  const cookieString = req.headers.get('cookie');
  const hasAccessToken =
    cookieString && cookieString.search('access_token') >= 0;
  const { pathname } = req.nextUrl;

  // If user has access token and is on signin page, redirect to projects
  if (hasAccessToken && pathname === '/signin') {
    return NextResponse.redirect(new URL('/projects', req.url));
  }

  // If user doesn't have access token and is not on signin page, redirect to signin
  if (!hasAccessToken && pathname !== '/signin') {
    return NextResponse.redirect(new URL('/signin', req.url));
  }

  // Allow the request to continue
  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    '/((?!api|_next/static|_next|images|icons|login|favicon.ico|authorized).*)',
  ],
};
