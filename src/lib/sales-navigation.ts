import { ShoppingCart, type LucideIcon } from 'lucide-react';

export interface SalesNavItem {
  title: string;
  url: string;
  icon: LucideIcon;
  isActive?: boolean;
  items?: SalesNavSubItem[];
}

export interface SalesNavSubItem {
  title: string;
  url: string;
}

export interface SalesProjectItem {
  name: string;
  url: string;
  icon: LucideIcon;
}

export interface SalesWorkspace {
  id: string;
  name: string;
  description?: string;
}

export interface SalesNavigationData {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
  workspaces: SalesWorkspace[];
  currentWorkspace: SalesWorkspace;
  navMain: SalesNavItem[];
  navSecondary: SalesNavItem[];
  projects: SalesProjectItem[];
}

export const salesNavigationData: SalesNavigationData = {
  user: {
    name: 'Sales User',
    email: '<EMAIL>',
    avatar: '/avatars/blank-profile.webp',
  },
  workspaces: [
    {
      id: '1',
      name: 'Sales Workspace',
      description: 'Main sales operations',
    },
    {
      id: '2',
      name: 'Regional Sales',
      description: 'Regional sales team',
    },
  ],
  currentWorkspace: {
    id: '1',
    name: 'Sales Workspace',
    description: 'Main sales operations',
  },
  navMain: [
    {
      title: 'My Orders',
      url: '/my-orders',
      icon: ShoppingCart,
    },
  ],
  navSecondary: [
    // {
    //   title: 'Support',
    //   url: '#',
    //   icon: LifeBuoy,
    // },
    // {
    //   title: 'Feedback',
    //   url: '#',
    //   icon: Send,
    // },
  ],
  projects: [],
};
