/**
 * Authentication utilities for token validation and JWT handling
 */

/**
 * Decode JWT token without verification (for client-side expiration check)
 * Note: This is only for reading the payload, not for security validation
 */
export function decodeJWT(token: string): any {
  try {
    // Validate token format
    if (!token || typeof token !== 'string') {
      return null;
    }

    // JWT has 3 parts separated by dots: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    // Decode the payload (second part)
    const payload = parts[1];

    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - (payload.length % 4)) % 4);

    // Decode base64 - handle both browser and Node.js environments
    let decodedPayload: string;
    if (typeof atob !== 'undefined') {
      // Browser environment
      decodedPayload = atob(paddedPayload);
    } else {
      // Node.js environment (middleware runs on server)
      decodedPayload = Buffer.from(paddedPayload, 'base64').toString('utf-8');
    }

    // Parse JSON
    return JSON.parse(decodedPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

/**
 * Check if JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const decoded = decodeJWT(token);

    if (!decoded || !decoded.exp) {
      // If we can't decode or there's no expiration, consider it expired
      return true;
    }

    // JWT exp is in seconds, Date.now() is in milliseconds
    const currentTime = Math.floor(Date.now() / 1000);

    return decoded.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Consider expired if there's an error
  }
}

/**
 * Validate access token from cookies
 */
export function validateAccessToken(token: string | undefined): boolean {
  if (!token) {
    return false;
  }

  // Check if token is expired
  if (isTokenExpired(token)) {
    return false;
  }

  return true;
}

/**
 * Get token expiration time in milliseconds
 */
export function getTokenExpiration(token: string): number | null {
  try {
    const decoded = decodeJWT(token);

    if (!decoded || !decoded.exp) {
      return null;
    }

    // Convert from seconds to milliseconds
    return decoded.exp * 1000;
  } catch (error) {
    console.error('Error getting token expiration:', error);
    return null;
  }
}

/**
 * Check if token will expire within a certain time (in minutes)
 */
export function isTokenExpiringSoon(
  token: string,
  minutesThreshold: number = 5
): boolean {
  try {
    const expiration = getTokenExpiration(token);

    if (!expiration) {
      return true; // Consider expiring soon if we can't get expiration
    }

    const thresholdTime = Date.now() + minutesThreshold * 60 * 1000;

    return expiration < thresholdTime;
  } catch (error) {
    console.error('Error checking if token is expiring soon:', error);
    return true;
  }
}

/**
 * Extract user information from JWT token
 */
export function getUserFromToken(token: string): any {
  try {
    const decoded = decodeJWT(token);

    if (!decoded) {
      return null;
    }

    // Return user information (adjust based on your JWT structure)
    return {
      id: decoded.sub || decoded.id,
      username: decoded.username,
      email: decoded.email,
      name: decoded.name,
      exp: decoded.exp,
      iat: decoded.iat,
    };
  } catch (error) {
    console.error('Error extracting user from token:', error);
    return null;
  }
}

/**
 * User type definitions for role-based access control
 */
export interface UserType {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  is_admin: boolean;
  is_member?: boolean;
  is_sale?: boolean;
}

export interface UserProfile {
  id: number;
  name: string;
  email: string;
  user_type: UserType;
}

/**
 * Route access control definitions
 */
export const ROUTE_ACCESS = {
  // Admin and Member accessible routes
  ADMIN_MEMBER_ROUTES: ['/dashboard', '/clusters', '/projects'],
  // Sale user accessible routes
  SALE_ROUTES: ['/my-orders'],
  // Public routes (no authentication required)
  PUBLIC_ROUTES: ['/signin', '/signup', '/access-denied'],
} as const;

/**
 * Default routes for each user type
 */
export const DEFAULT_ROUTES = {
  ADMIN: '/dashboard',
  MEMBER: '/dashboard',
  SALE: '/my-orders',
} as const;

/**
 * Determine user role from user type
 */
export function getUserRole(
  userType: UserType
): 'admin' | 'member' | 'sale' | 'unknown' {
  if (userType.is_admin) {
    return 'admin';
  }
  if (userType.is_sale) {
    return 'sale';
  }
  // If not admin or sale, treat as member (default for regular users)
  if (userType.is_member !== false) {
    return 'member';
  }
  return 'unknown';
}

/**
 * Get default route for user based on their role
 */
export function getDefaultRouteForUser(userType: UserType): string {
  const role = getUserRole(userType);

  switch (role) {
    case 'admin':
      return DEFAULT_ROUTES.ADMIN;
    case 'member':
      return DEFAULT_ROUTES.MEMBER;
    case 'sale':
      return DEFAULT_ROUTES.SALE;
    default:
      return '/signin';
  }
}

/**
 * Check if user has access to a specific route
 */
export function hasRouteAccess(userType: UserType, pathname: string): boolean {
  const role = getUserRole(userType);

  // Check if it's a public route
  if (ROUTE_ACCESS.PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    return true;
  }

  // Check role-specific access
  switch (role) {
    case 'admin':
    case 'member':
      return ROUTE_ACCESS.ADMIN_MEMBER_ROUTES.some(route =>
        pathname.startsWith(route)
      );
    case 'sale':
      return ROUTE_ACCESS.SALE_ROUTES.some(route => pathname.startsWith(route));
    default:
      return false;
  }
}
