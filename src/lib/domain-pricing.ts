import { DomainCheckResult } from '@/store/namecheap/type';

/**
 * Extracts the price from a domain availability check result
 * @param result - The domain check result from Namecheap API
 * @returns The price as a number, or 0 if no price is available
 */

export function extractDomainPrice(result: DomainCheckResult | null): number {
  if (!result) {
    return 0;
  }

  const isAvailable = result.Available === 'true';
  const isPremium = result.IsPremiumName === 'true';

  // If domain is not available, return 0
  if (!isAvailable) {
    return 0;
  }

  // For premium domains, use PremiumRegistrationPrice
  if (isPremium && result.PremiumRegistrationPrice) {
    const price = parseFloat(result.PremiumRegistrationPrice);
    return isNaN(price) ? 0 : price;
  }

  // For regular domains, use the first price from the Price array
  if (!isPremium && result.Price && result.Price.length > 0) {
    const price = parseFloat(result.Price[0].Price);
    return isNaN(price) ? 0 : price;
  }

  // Default fallback
  return 0;
}

/**
 * Extracts prices for multiple domains from domain check results
 * @param domains - Array of domain names
 * @param domainCheckResults - Array of domain check results
 * @returns Object mapping domain names to their prices
 */
export function extractDomainPrices(
  domains: string[],
  domainCheckResults: DomainCheckResult[]
): Record<string, number> {
  const priceMap: Record<string, number> = {};

  domains.forEach(domain => {
    const result: any = domainCheckResults.find(r => r.Domain === domain);
    priceMap[domain] = extractDomainPrice(result);
  });

  return priceMap;
}
