import {
  <PERSON><PERSON><PERSON><PERSON>,
  Clock,
  AlertCircle,
  User,
  FileText,
  MessageSquare,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface ActivityItem {
  id: string;
  type:
    | 'task_completed'
    | 'task_created'
    | 'comment_added'
    | 'user_joined'
    | 'deadline_approaching';
  title: string;
  description: string;
  timestamp: string;
  user?: {
    name: string;
    avatar?: string;
    initials: string;
  };
}

function getActivityIcon(type: ActivityItem['type']) {
  switch (type) {
    case 'task_completed':
      return <CheckCircle className='h-4 w-4 text-green-500' />;
    case 'task_created':
      return <FileText className='h-4 w-4 text-blue-500' />;
    case 'comment_added':
      return <MessageSquare className='h-4 w-4 text-purple-500' />;
    case 'user_joined':
      return <User className='h-4 w-4 text-green-500' />;
    case 'deadline_approaching':
      return <AlertCircle className='h-4 w-4 text-orange-500' />;
    default:
      return <Clock className='h-4 w-4 text-gray-500' />;
  }
}

function ActivityItemComponent({ item }: { item: ActivityItem }) {
  return (
    <div className='flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors duration-200'>
      <div className='flex-shrink-0 mt-1'>{getActivityIcon(item.type)}</div>
      <div className='flex-1 min-w-0'>
        <div className='flex items-center justify-between'>
          <p className='text-sm font-medium text-foreground truncate'>
            {item.title}
          </p>
          <span className='text-xs text-muted-foreground whitespace-nowrap ml-2'>
            {item.timestamp}
          </span>
        </div>
        <p className='text-sm text-muted-foreground mt-1'>{item.description}</p>
        {item.user && (
          <div className='flex items-center space-x-2 mt-2'>
            <Avatar className='h-6 w-6'>
              <AvatarImage src={item.user.avatar} alt={item.user.name} />
              <AvatarFallback className='text-xs'>
                {item.user.initials}
              </AvatarFallback>
            </Avatar>
            <span className='text-xs text-muted-foreground'>
              {item.user.name}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

export function RecentActivity() {
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'task_completed',
      title: 'Website redesign completed',
      description: 'The new homepage design has been finalized and approved.',
      timestamp: '2 hours ago',
      user: {
        name: 'Sarah Johnson',
        initials: 'SJ',
        avatar: '/avatars/blank-profile.webp',
      },
    },
    {
      id: '2',
      type: 'comment_added',
      title: 'New comment on Mobile App project',
      description: 'Added feedback on the user interface mockups.',
      timestamp: '4 hours ago',
      user: {
        name: 'Mike Chen',
        initials: 'MC',
        avatar: '/avatars/blank-profile.webp',
      },
    },
    {
      id: '3',
      type: 'task_created',
      title: 'New task: API Integration',
      description: 'Integrate payment gateway with the checkout system.',
      timestamp: '6 hours ago',
      user: {
        name: 'Alex Rodriguez',
        initials: 'AR',
        avatar: '/avatars/blank-profile.webp',
      },
    },
    {
      id: '4',
      type: 'deadline_approaching',
      title: 'Deadline reminder',
      description: 'E-commerce platform launch is due in 3 days.',
      timestamp: '8 hours ago',
    },
    {
      id: '5',
      type: 'user_joined',
      title: 'New team member joined',
      description: 'Emma Wilson has joined the development team.',
      timestamp: '1 day ago',
      user: {
        name: 'Emma Wilson',
        initials: 'EW',
        avatar: '/avatars/blank-profile.webp',
      },
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>
          Latest updates and activities from your projects
        </CardDescription>
      </CardHeader>
      <CardContent className='p-0'>
        <div className='space-y-1'>
          {activities.map(activity => (
            <ActivityItemComponent key={activity.id} item={activity} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
