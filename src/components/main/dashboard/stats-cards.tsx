import {
  TrendingUp,
  TrendingDown,
  Users,
  Activity,
  DollarSign,
  Target,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface StatCardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  trend?: {
    value: string;
    isPositive: boolean;
  };
}

function StatCard({ title, value, description, icon, trend }: StatCardProps) {
  return (
    <Card className='hover:shadow-lg transition-shadow duration-200'>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium text-muted-foreground'>
          {title}
        </CardTitle>
        <div className='h-4 w-4 text-muted-foreground'>{icon}</div>
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-medium'>{value}</div>
        <div className='flex items-center space-x-2 text-xs text-muted-foreground'>
          <span>{description}</span>
          {trend && (
            <div className='flex items-center space-x-1'>
              {trend.isPositive ? (
                <TrendingUp className='h-3 w-3 text-green-500' />
              ) : (
                <TrendingDown className='h-3 w-3 text-red-500' />
              )}
              <span
                className={trend.isPositive ? 'text-green-500' : 'text-red-500'}
              >
                {trend.value}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function StatsCards() {
  const stats = [
    {
      title: 'Total Projects',
      value: '12',
      description: 'Active projects',
      icon: <Target className='h-4 w-4' />,
      trend: {
        value: '+2 this month',
        isPositive: true,
      },
    },
    {
      title: 'Team Members',
      value: '8',
      description: 'Active collaborators',
      icon: <Users className='h-4 w-4' />,
      trend: {
        value: '+1 this week',
        isPositive: true,
      },
    },
    {
      title: 'Tasks Completed',
      value: '247',
      description: 'This month',
      icon: <Activity className='h-4 w-4' />,
      trend: {
        value: '+12% from last month',
        isPositive: true,
      },
    },
    {
      title: 'Revenue',
      value: '$45,231',
      description: 'Total earnings',
      icon: <DollarSign className='h-4 w-4' />,
      trend: {
        value: '+8.2% from last month',
        isPositive: true,
      },
    },
  ];

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
}
