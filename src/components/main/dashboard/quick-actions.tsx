'use client';

import {
  Plus,
  FileText,
  Users,
  Settings,
  Calendar,
  MessageSquare,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick?: () => void;
  variant?: 'default' | 'secondary' | 'outline';
}

function QuickActionButton({
  title,
  description,
  icon,
  onClick,
  variant = 'outline',
}: QuickActionProps) {
  return (
    <Button
      variant={variant}
      className='h-auto p-4 flex flex-col items-center space-y-2 hover:scale-105 transition-transform duration-200'
      onClick={onClick}
    >
      <div className='h-8 w-8 flex items-center justify-center'>{icon}</div>
      <div className='text-center'>
        <div className='font-medium text-sm'>{title}</div>
        <div className='text-xs text-muted-foreground'>{description}</div>
      </div>
    </Button>
  );
}

export function QuickActions() {
  const actions = [
    {
      title: 'New Project',
      description: 'Start a new project',
      icon: <Plus className='h-5 w-5' />,
      variant: 'default' as const,
      onClick: () => console.log('Create new project'),
    },
    {
      title: 'Create Task',
      description: 'Add a new task',
      icon: <FileText className='h-5 w-5' />,
      onClick: () => console.log('Create new task'),
    },
    {
      title: 'Invite Team',
      description: 'Add team members',
      icon: <Users className='h-5 w-5' />,
      onClick: () => console.log('Invite team members'),
    },
    {
      title: 'Schedule Meeting',
      description: 'Book a meeting',
      icon: <Calendar className='h-5 w-5' />,
      onClick: () => console.log('Schedule meeting'),
    },
    {
      title: 'Send Message',
      description: 'Contact team',
      icon: <MessageSquare className='h-5 w-5' />,
      onClick: () => console.log('Send message'),
    },
    {
      title: 'Settings',
      description: 'Manage preferences',
      icon: <Settings className='h-5 w-5' />,
      onClick: () => console.log('Open settings'),
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>
          Frequently used actions to help you get things done faster
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className='grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4'>
          {actions.map((action, index) => (
            <QuickActionButton key={index} {...action} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
