'use client';

import { Edit, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useSalesStore } from '@/store/sales/action';
import { SaleUserType } from '@/store/sales/type';

interface EditSaleModalProps {
  sale: SaleUserType | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface FormData {
  name: string;
  email: string;
}

export function EditSaleModal({
  sale,
  open,
  onOpenChange,
}: EditSaleModalProps) {
  const { updateSale, updating } = useSalesStore();

  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when sale changes or modal opens
  useEffect(() => {
    if (sale && open) {
      setFormData({
        name: sale.name,
        email: sale.email,
      });
      setErrors({});
    }
  }, [sale, open]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Username is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!sale) {
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      const response = await updateSale(sale.id, formData);

      if (response?.status) {
        toast.success('Sale user updated successfully');
        onOpenChange(false);
      } else {
        toast.error(response?.message || 'Failed to update sale user');
      }
    } catch (error: any) {
      toast.error(
        error?.response?.data?.message || 'Failed to update sale user'
      );
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleClose = () => {
    if (!updating) {
      setFormData({ name: '', email: '' });
      setErrors({});
      onOpenChange(false);
    }
  };

  if (!sale) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Edit className='h-5 w-5' />
            Edit Sale User
          </DialogTitle>
          <DialogDescription>
            Update the sale user information. Changes will be saved immediately.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className='grid gap-4 py-4'>
            <div className='space-y-2'>
              <Label htmlFor='edit-name'>Name</Label>
              <Input
                id='edit-name'
                placeholder='Enter full name'
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                disabled={updating}
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className='text-sm text-destructive'>{errors.name}</p>
              )}
            </div>
            <div className='space-y-2'>
              <Label htmlFor='edit-email'>Username</Label>
              <Input
                id='edit-email'
                type='text'
                placeholder='Enter username'
                value={formData.email}
                onChange={e => handleInputChange('email', e.target.value)}
                disabled={updating}
                className={errors.email ? 'border-destructive' : ''}
              />
              {errors.email && (
                <p className='text-sm text-destructive'>{errors.email}</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={updating}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={updating}>
              {updating ? (
                <>
                  <Loader2 className='h-4 w-4 animate-spin mr-2' />
                  Updating...
                </>
              ) : (
                <>
                  <Edit className='h-4 w-4 mr-2' />
                  Update Sale
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
