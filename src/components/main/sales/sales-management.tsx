'use client';

import {
  Search,
  Users,
  RefreshCw,
  Edit,
  Trash2,
  Calendar,
  MoreHorizontal,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDebounce } from '@/hooks/use-debounce';
import { useSalesStore } from '@/store/sales/action';
import { GetSalesParams } from '@/store/sales/type';

interface SalesManagementProps {
  showSearch?: boolean;
  tableView?: boolean;
}

export function SalesManagement({
  showSearch = true,
  tableView = false,
}: SalesManagementProps) {
  const { sales, loading, fetchSales } = useSalesStore();
  const [searchName, setSearchName] = useState('');
  const [searchEmail, setSearchEmail] = useState('');
  const debouncedSearchName = useDebounce(searchName, 500);
  const debouncedSearchEmail = useDebounce(searchEmail, 500);

  const loadSales = useCallback(() => {
    const filters: GetSalesParams = {};

    if (debouncedSearchName.trim()) {
      filters.name = debouncedSearchName.trim();
    }
    if (debouncedSearchEmail.trim()) {
      filters.email = debouncedSearchEmail.trim();
    }

    fetchSales(Object.keys(filters).length > 0 ? filters : undefined);
  }, [debouncedSearchName, debouncedSearchEmail, fetchSales]);

  useEffect(() => {
    loadSales();
  }, [loadSales]);

  const handleReset = () => {
    setSearchName('');
    setSearchEmail('');
  };

  const handleRefresh = () => {
    loadSales();
  };

  const handleEditSale = (saleId: number) => {
    // TODO: Implement edit functionality
    console.log('Edit sale:', saleId);
  };

  const handleDeleteSale = (saleId: number) => {
    // TODO: Implement delete functionality
    console.log('Delete sale:', saleId);
  };

  const renderSearchFilters = () => (
    <Card className='mb-6'>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center gap-2'>
            <Search className='h-5 w-5' />
            Search & Filter
          </CardTitle>
          <div className='flex gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw
                className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className='flex gap-4 items-end'>
          <div className='flex-1'>
            <label className='text-sm font-medium mb-2 block'>Name</label>
            <Input
              placeholder='Search by name...'
              value={searchName}
              onChange={e => setSearchName(e.target.value)}
            />
          </div>
          <div className='flex-1'>
            <label className='text-sm font-medium mb-2 block'>Email</label>
            <Input
              placeholder='Search by email...'
              value={searchEmail}
              onChange={e => setSearchEmail(e.target.value)}
            />
          </div>
          <div className='flex gap-2'>
            <Button variant='outline' onClick={handleReset} disabled={loading}>
              Reset
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderTableView = () => (
    <Card>
      <CardHeader>
        <CardTitle>Sales Team Members</CardTitle>
        <p className='text-sm text-muted-foreground'>
          {loading ? 'Loading...' : `${sales.length} sales users found`}
        </p>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <RefreshCw className='h-4 w-4 animate-spin' />
              <span>Loading sales users...</span>
            </div>
          </div>
        ) : sales.length === 0 ? (
          <div className='text-center py-12'>
            <Users className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
            <h3 className='text-lg font-medium mb-2'>No sales users found</h3>
            <p className='text-muted-foreground mb-4'>
              {searchName || searchEmail
                ? 'Try adjusting your search criteria'
                : 'No sales users are currently registered in the system'}
            </p>
            {(searchName || searchEmail) && (
              <Button variant='outline' onClick={handleReset}>
                Clear filters
              </Button>
            )}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className='w-[50px]'>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sales.map(sale => (
                <TableRow key={sale.id}>
                  <TableCell className='font-medium'>{sale.name}</TableCell>
                  <TableCell className='font-mono text-sm'>
                    {sale.email}
                  </TableCell>
                  <TableCell>
                    <Badge variant='secondary'>{sale.user_type.name}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        sale.user_type.is_active ? 'default' : 'destructive'
                      }
                    >
                      {sale.user_type.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell className='text-sm text-muted-foreground'>
                    {new Date(sale.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' size='sm'>
                          <MoreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuItem
                          onClick={() => handleEditSale(sale.id)}
                        >
                          <Edit className='h-4 w-4 mr-2' />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteSale(sale.id)}
                          className='text-destructive focus:text-destructive'
                        >
                          <Trash2 className='h-4 w-4 mr-2' />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );

  const renderCardView = () => (
    <Card>
      <CardHeader>
        <CardTitle>Sales Team Members</CardTitle>
        <p className='text-sm text-muted-foreground'>
          {loading ? 'Loading...' : `${sales.length} sales users found`}
        </p>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <RefreshCw className='h-4 w-4 animate-spin' />
              <span>Loading sales users...</span>
            </div>
          </div>
        ) : sales.length === 0 ? (
          <div className='text-center py-12'>
            <Users className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
            <h3 className='text-lg font-medium mb-2'>No sales users found</h3>
            <p className='text-muted-foreground mb-4'>
              {searchName || searchEmail
                ? 'Try adjusting your search criteria'
                : 'No sales users are currently registered in the system'}
            </p>
            {(searchName || searchEmail) && (
              <Button variant='outline' onClick={handleReset}>
                Clear filters
              </Button>
            )}
          </div>
        ) : (
          <div className='space-y-4'>
            {sales.map(sale => (
              <div
                key={sale.id}
                className='border rounded-lg p-4 hover:bg-muted/50 transition-colors'
              >
                <div className='flex items-start justify-between'>
                  <div className='space-y-2 flex-1'>
                    <div className='flex items-center gap-3'>
                      <h3 className='font-medium text-lg'>{sale.name}</h3>
                      <Badge
                        variant={
                          sale.user_type.is_active ? 'default' : 'secondary'
                        }
                      >
                        {sale.user_type.name}
                      </Badge>
                      {sale.user_type.is_sale && (
                        <Badge variant='outline'>Sales</Badge>
                      )}
                      {!sale.user_type.is_active && (
                        <Badge variant='destructive'>Inactive</Badge>
                      )}
                    </div>
                    <p className='text-sm text-muted-foreground font-mono'>
                      {sale.email}
                    </p>
                    <p className='text-sm text-muted-foreground'>
                      {sale.user_type.description || 'No description'}
                    </p>
                    <div className='flex gap-2 pt-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleEditSale(sale.id)}
                      >
                        <Edit className='h-4 w-4 mr-2' />
                        Edit
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleDeleteSale(sale.id)}
                        className='text-destructive hover:text-destructive'
                      >
                        <Trash2 className='h-4 w-4 mr-2' />
                        Delete
                      </Button>
                    </div>
                  </div>
                  <div className='text-right text-xs text-muted-foreground space-y-1'>
                    <div className='font-mono'>ID: {sale.id}</div>
                    <div className='flex items-center gap-1'>
                      <Calendar className='h-3 w-3' />
                      {new Date(sale.created_at).toLocaleDateString()}
                    </div>
                    <div>
                      Updated: {new Date(sale.updated_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className='space-y-6'>
      {showSearch && renderSearchFilters()}
      {tableView ? renderTableView() : renderCardView()}
    </div>
  );
}
