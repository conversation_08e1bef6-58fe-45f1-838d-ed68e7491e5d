'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useClusterStore } from '@/store/cluster/action';
import type { ClusterType } from '@/store/cluster/type';
import { useWorkspaceStore } from '@/store/workspace/action';

const editClusterSchema = z.object({
  name: z
    .string()
    .min(1, 'Cluster name is required')
    .max(50, 'Name must be less than 50 characters'),
  region: z.string().min(1, 'Region is required'),
  pool_name: z.string().min(1, 'Pool name is required'),
  size: z.string().min(1, 'Size is required'),
  node_count: z
    .number()
    .min(1, 'Node count must be at least 1')
    .max(100, 'Node count cannot exceed 100'),
});

type EditClusterForm = z.infer<typeof editClusterSchema>;

interface EditClusterModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cluster: ClusterType | null;
}

// Available regions and sizes options
const REGIONS = [
  { value: 'sgp1', label: 'Singapore (sgp1)' },
  { value: 'nyc1', label: 'New York (nyc1)' },
  { value: 'sfo3', label: 'San Francisco (sfo3)' },
  { value: 'fra1', label: 'Frankfurt (fra1)' },
  { value: 'lon1', label: 'London (lon1)' },
  { value: 'tor1', label: 'Toronto (tor1)' },
];

const SIZES = [
  { value: 's-1vcpu-2gb', label: 'Basic (1 vCPU, 2GB RAM)' },
  { value: 's-2vcpu-4gb', label: 'Standard (2 vCPU, 4GB RAM)' },
  { value: 's-4vcpu-8gb', label: 'Performance (4 vCPU, 8GB RAM)' },
  { value: 's-8vcpu-16gb', label: 'High Performance (8 vCPU, 16GB RAM)' },
];

export function EditClusterModal({
  open,
  onOpenChange,
  cluster,
}: EditClusterModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { updateCluster } = useClusterStore();
  const { selectedWorkspace } = useWorkspaceStore();

  const form = useForm<EditClusterForm>({
    resolver: zodResolver(editClusterSchema),
    defaultValues: {
      name: '',
      region: '',
      pool_name: 'default-pool',
      size: '',
      node_count: 1,
    },
  });

  // Update form values when cluster changes
  useEffect(() => {
    if (cluster) {
      form.reset({
        name: cluster.name,
        region: cluster.region,
        pool_name: cluster.pool_name,
        size: cluster.size,
        node_count: cluster.node_count,
      });
    }
  }, [cluster, form]);

  const onSubmit = async (data: EditClusterForm) => {
    if (!cluster || !selectedWorkspace) {
      console.error('No cluster or workspace selected');
      return;
    }

    console.log('cluster', cluster);

    setIsSubmitting(true);
    try {
      const success = await updateCluster(cluster.id, {
        ...data,
        workspace_id: selectedWorkspace.id,
        status_id: cluster?.status?.id === 1 ? 1 : 6, // Preserve current status
      });
      if (success) {
        form.reset();
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Failed to update cluster:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
      if (!newOpen) {
        form.reset();
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>Edit Cluster</DialogTitle>
          <DialogDescription>
            Update your cluster configuration. Changes will be applied to the
            cluster.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cluster Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter cluster name'
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='region'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Region</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select region' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {REGIONS.map(region => (
                        <SelectItem key={region.value} value={region.value}>
                          {region.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='size'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Node Size</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select node size' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {SIZES.map(size => (
                        <SelectItem key={size.value} value={size.value}>
                          {size.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='pool_name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pool Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Enter pool name'
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='node_count'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Node Count</FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='1'
                        {...field}
                        onChange={e =>
                          field.onChange(parseInt(e.target.value) || 1)
                        }
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => handleOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? 'Updating...' : 'Update Cluster'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
