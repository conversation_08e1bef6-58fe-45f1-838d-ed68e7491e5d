'use client';

import { MainBreadcrumb } from '@/components/main/layout/main-breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { useAuthStore } from '@/store/auth/action';

export function MainHeader() {
  const { me } = useAuthStore();

  return (
    <header className='flex h-16 shrink-0 items-center gap-2 justify-between'>
      <div className='flex items-center gap-2 px-4'>
        <SidebarTrigger className='-ml-1' />
        <Separator orientation='vertical' className='mr-2 h-4' />
        <MainBreadcrumb />
      </div>
      {me && (
        <div className='flex items-center gap-2 px-4'>
          <span className='text-sm text-muted-foreground'>Welcome back,</span>
          <span className='text-sm font-medium'>{me.name}</span>
          {me.user_type.is_admin && (
            <span className='text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full'>
              Admin
            </span>
          )}
        </div>
      )}
    </header>
  );
}
