'use client';

import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import type { Workspace } from '@/lib/navigation';
import { cn } from '@/lib/utils';

interface WorkspaceSelectorProps {
  workspaces: Workspace[];
  currentWorkspace: Workspace;
  onWorkspaceSelect: (workspace: Workspace) => void;
  onCreateWorkspace: () => void;
  loading?: boolean;
}

export function WorkspaceSelector({
  workspaces,
  currentWorkspace,
  onWorkspaceSelect,
  onCreateWorkspace,
  loading = false,
}: WorkspaceSelectorProps) {
  const [open, setOpen] = React.useState(false);

  if (loading) {
    return (
      <div className='w-full p-3'>
        <div className='flex items-center gap-3'>
          <Skeleton className='h-8 w-8 rounded-lg' />
          <div className='grid flex-1 gap-1'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-3 w-32' />
          </div>
          <Skeleton className='h-4 w-4' />
        </div>
      </div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='ghost'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between h-auto p-3'
        >
          <div className='flex items-center gap-3'>
            <div className='flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground'>
              <div className='size-4 rounded bg-white/20' />
            </div>
            <div className='grid flex-1 text-left text-sm leading-tight'>
              <span className='truncate font-medium'>
                {currentWorkspace.name}
              </span>
              <span className='truncate text-xs text-muted-foreground'>
                {currentWorkspace.description || 'Workspace'}
              </span>
            </div>
          </div>
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-64 p-0' align='start'>
        <div className='p-2'>
          <div className='text-xs font-medium text-muted-foreground px-2 py-1.5'>
            Select Workspace
          </div>
          <div className='space-y-1'>
            {workspaces.length === 0 ? (
              <div className='px-2 py-1.5 text-sm text-muted-foreground'>
                No workspaces found
              </div>
            ) : (
              workspaces.map(workspace => (
                <Button
                  key={workspace.id}
                  variant='ghost'
                  className={cn(
                    'w-full justify-start h-auto p-2',
                    currentWorkspace.id === workspace.id && 'bg-accent'
                  )}
                  onClick={() => {
                    onWorkspaceSelect(workspace);
                    setOpen(false);
                  }}
                >
                  <div className='flex items-center gap-3 w-full'>
                    <div className='flex aspect-square size-6 items-center justify-center rounded bg-sidebar-primary text-sidebar-primary-foreground'>
                      <div className='size-3 rounded bg-white/20' />
                    </div>
                    <div className='grid flex-1 text-left text-sm'>
                      <span className='truncate font-medium'>
                        {workspace.name}
                      </span>
                      {workspace.description && (
                        <span className='truncate text-xs text-muted-foreground'>
                          {workspace.description}
                        </span>
                      )}
                    </div>
                    {currentWorkspace.id === workspace.id && (
                      <Check className='h-4 w-4' />
                    )}
                  </div>
                </Button>
              ))
            )}
          </div>
          <div className='border-t mt-2 pt-2'>
            <Button
              variant='ghost'
              className='w-full justify-start h-auto p-2'
              onClick={() => {
                onCreateWorkspace();
                setOpen(false);
              }}
            >
              <div className='flex items-center gap-3'>
                <div className='flex aspect-square size-6 items-center justify-center rounded border border-dashed'>
                  <Plus className='h-4 w-4' />
                </div>
                <span className='text-sm font-medium'>Create Workspace</span>
              </div>
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
