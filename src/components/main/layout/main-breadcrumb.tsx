'use client';

import { usePathname } from 'next/navigation';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { generateBreadcrumbs } from '@/lib/navigation';

export function MainBreadcrumb() {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname);

  if (breadcrumbs.length === 0) {
    return null;
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((breadcrumb, index) => (
          <div key={`${breadcrumb.url}-${index}`} className='flex items-center'>
            {index > 0 && <BreadcrumbSeparator className='hidden md:block' />}
            <BreadcrumbItem
              className={`${index === 0 ? 'hidden md:block' : ''} ${
                breadcrumb.isCurrent
                  ? 'text-foreground'
                  : 'text-muted-foreground'
              }`}
            >
              {breadcrumb.isCurrent ? (
                <BreadcrumbPage className='font-medium'>
                  {breadcrumb.title}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink
                  href={breadcrumb.url}
                  className='transition-colors hover:text-foreground'
                >
                  {breadcrumb.title}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
