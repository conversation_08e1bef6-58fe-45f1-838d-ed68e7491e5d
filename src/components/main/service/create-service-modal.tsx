'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useServiceStore } from '@/store/service/action';

const createServiceSchema = z.object({
  name: z
    .string()
    .min(2, 'Service name must be at least 2 characters')
    .max(100, 'Service name must be at most 100 characters'),
  port: z.string().min(1, 'Port is required'),
  target_port: z.string().min(1, 'Target port is required'),
  type: z.enum(['ClusterIP', 'NodePort', 'LoadBalancer'], {
    required_error: 'Service type is required',
  }),
});

type CreateServiceForm = z.infer<typeof createServiceSchema>;

interface CreateServiceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deploymentId: number;
  deploymentName: string;
  deploymentContainerPort: number;
  namespaceId: number;
  onSuccess?: () => void;
}

export function CreateServiceModal({
  open,
  onOpenChange,
  deploymentId,
  deploymentName,
  deploymentContainerPort,
  namespaceId,
  onSuccess,
}: CreateServiceModalProps) {
  const { createService, creating } = useServiceStore();

  const form = useForm<CreateServiceForm>({
    resolver: zodResolver(createServiceSchema),
    defaultValues: {
      name: deploymentName,
      port: '80',
      target_port: deploymentContainerPort.toString(),
      type: 'ClusterIP',
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        name: deploymentName,
        port: '80',
        target_port: deploymentContainerPort.toString(),
        type: 'ClusterIP',
      });
    }
  }, [open, deploymentName, deploymentContainerPort, form]);

  const onSubmit = async (data: CreateServiceForm) => {
    try {
      const response = await createService({
        name: data.name,
        port: data.port,
        target_port: data.target_port,
        type: data.type,
        namespace_id: namespaceId,
        deployment_id: deploymentId,
      });

      if (response?.status) {
        toast.success('Service created successfully');
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to create service');
      }
    } catch (error) {
      console.error('Error creating service:', error);
      toast.error('Failed to create service');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Create Service</DialogTitle>
          <DialogDescription>
            {`Create a new service for the deployment "${deploymentName}". The
            service name and target port are automatically populated based on
            the deployment configuration.`}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <div className='space-y-4'>
              {/* Service Name */}
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='e.g., my-app-service'
                        {...field}
                        disabled={creating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Port */}
              <FormField
                control={form.control}
                name='port'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Port</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='e.g., 80'
                        {...field}
                        disabled={creating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Target Port */}
              <FormField
                control={form.control}
                name='target_port'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Port</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='e.g., 8080'
                        {...field}
                        disabled={creating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Service Type */}
              <FormField
                control={form.control}
                name='type'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={creating}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select service type' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='ClusterIP'>ClusterIP</SelectItem>
                        <SelectItem value='NodePort'>NodePort</SelectItem>
                        <SelectItem value='LoadBalancer'>
                          LoadBalancer
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={creating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creating}>
                {creating ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Creating...
                  </>
                ) : (
                  'Create Service'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
