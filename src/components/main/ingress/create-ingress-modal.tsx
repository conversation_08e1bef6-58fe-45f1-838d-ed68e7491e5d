'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Shield } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useIngressStore } from '@/store/ingress/action';

const createIngressSchema = z.object({
  name: z.string().min(1, 'Ingress name is required'),
  class: z.string().min(1, 'Ingress class is required'),
});

type CreateIngressForm = z.infer<typeof createIngressSchema>;

interface CreateIngressModalProps {
  namespaceId: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CreateIngressModal({
  namespaceId,
  open,
  onOpenChange,
  onSuccess,
}: CreateIngressModalProps) {
  const { createIngress, creating } = useIngressStore();

  const form = useForm<CreateIngressForm>({
    resolver: zodResolver(createIngressSchema),
    defaultValues: {
      name: '',
      class: 'nginx',
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        name: '',
        class: 'nginx',
      });
    }
  }, [open, form]);

  const onSubmit = async (data: CreateIngressForm) => {
    try {
      const response = await createIngress({
        ...data,
        namespace_id: namespaceId,
      });

      if (response?.status) {
        toast.success('Ingress created successfully');
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to create ingress');
      }
    } catch (error) {
      console.error('Error creating ingress:', error);
      toast.error('Failed to create ingress');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Shield className='h-5 w-5' />
            Create Ingress
          </DialogTitle>
          <DialogDescription>
            Create a new ingress to manage external access to your services.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ingress Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter ingress name'
                      {...field}
                      disabled={creating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='class'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ingress Class</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={creating}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select ingress class' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='nginx'>nginx</SelectItem>
                      <SelectItem value='traefik'>traefik</SelectItem>
                      <SelectItem value='haproxy'>haproxy</SelectItem>
                      <SelectItem value='istio'>istio</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={creating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creating}>
                {creating ? 'Creating...' : 'Create Ingress'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
