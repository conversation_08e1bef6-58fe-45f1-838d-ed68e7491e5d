'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Network } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useIngressSpecStore } from '@/store/ingress-spec/action';

const editIngressSpecSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  path: z.string().min(1, 'Path is required'),
  port: z
    .number()
    .min(1, 'Port must be at least 1')
    .max(65535, 'Port must be at most 65535'),
  service_id: z.number().min(1, 'Service is required'),
});

type EditIngressSpecForm = z.infer<typeof editIngressSpecSchema>;

interface Service {
  id: number;
  name: string;
  port: string;
  type: string;
}

interface IngressSpec {
  id: number;
  host: string;
  path: string;
  port: number;
  service_id: number;
  ingress_id: number;
}

interface EditIngressSpecModalProps {
  ingressSpec: IngressSpec;
  ingressName: string;
  services: Service[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditIngressSpecModal({
  ingressSpec,
  ingressName,
  services,
  open,
  onOpenChange,
  onSuccess,
}: EditIngressSpecModalProps) {
  const { updateIngressSpec, updating } = useIngressSpecStore();

  const form = useForm<EditIngressSpecForm>({
    resolver: zodResolver(editIngressSpecSchema),
    defaultValues: {
      host: ingressSpec.host,
      path: ingressSpec.path,
      port: ingressSpec.port,
      service_id: ingressSpec.service_id,
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        host: ingressSpec.host,
        path: ingressSpec.path,
        port: ingressSpec.port,
        service_id: ingressSpec.service_id,
      });
    }
  }, [open, ingressSpec, form]);

  const onSubmit = async (data: EditIngressSpecForm) => {
    try {
      const response = await updateIngressSpec(ingressSpec.id, {
        ...data,
      });

      if (response?.status) {
        toast.success('Ingress spec updated successfully');
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to update ingress spec');
      }
    } catch (error) {
      console.error('Error updating ingress spec:', error);
      toast.error('Failed to update ingress spec');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Network className='h-5 w-5' />
            Edit Ingress Spec
          </DialogTitle>
          <DialogDescription>
            {`Update the route specification for "${ingressName}" ingress.`}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='host'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Host</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='example.com'
                      {...field}
                      disabled={updating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='path'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Path</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='/api/v1'
                      {...field}
                      disabled={updating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='port'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Port</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      placeholder='80'
                      {...field}
                      onChange={e =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                      disabled={updating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='service_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value))}
                    defaultValue={field.value?.toString()}
                    disabled={updating || services.length === 0}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            services.length === 0
                              ? 'No services available'
                              : 'Select a service'
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {services.map(service => (
                        <SelectItem
                          key={service.id}
                          value={service.id.toString()}
                        >
                          <div className='flex flex-col'>
                            <span className='font-medium'>{service.name}</span>
                            <span className='text-xs text-muted-foreground'>
                              Port: {service.port} • Type: {service.type}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                  {services.length === 0 && (
                    <p className='text-xs text-muted-foreground'>
                      No services available in this project. Create a service
                      first.
                    </p>
                  )}
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={updating}
              >
                Cancel
              </Button>
              <Button
                type='submit'
                disabled={updating || services.length === 0}
              >
                {updating ? 'Updating...' : 'Update Ingress Spec'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
