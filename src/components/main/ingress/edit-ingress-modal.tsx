'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Shield } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { updateIngressStatus } from '@/actions/ingress';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useIngressStore } from '@/store/ingress/action';
import { IngressType } from '@/store/ingress/type';

const editIngressSchema = z.object({
  name: z.string().min(1, 'Ingress name is required'),
  class: z.string().min(1, 'Ingress class is required'),
  status_id: z.number().min(1, 'Status is required'),
});

type EditIngressForm = z.infer<typeof editIngressSchema>;

interface EditIngressModalProps {
  ingress: IngressType;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditIngressModal({
  ingress,
  open,
  onOpenChange,
  onSuccess,
}: EditIngressModalProps) {
  const { updateIngress, updating } = useIngressStore();

  const form = useForm<EditIngressForm>({
    resolver: zodResolver(editIngressSchema),
    defaultValues: {
      name: ingress.name,
      class: ingress.class,
      status_id: ingress.status?.id || 1,
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        name: ingress.name,
        class: ingress.class,
        status_id: ingress.status?.id || 1,
      });
    }
  }, [open, ingress, form]);

  const onSubmit = async (data: EditIngressForm) => {
    try {
      console.log('data', data);
      console.log('ingress', ingress);
      const response: any = await updateIngress(ingress.id, {
        ...data,
      });
      if (response?.status) {
        toast.success('Ingress updated successfully');
        onOpenChange(false);

        // Update ingress status to 6 after successful ingress update
        await updateIngressStatus(ingress.id, { status_id: 6 });

        onSuccess?.();
      } else {
        toast.error('Failed to update ingress');
      }
    } catch (error) {
      console.error('Error updating ingress:', error);
      toast.error('Failed to update ingress');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Shield className='h-5 w-5' />
            Edit Ingress
          </DialogTitle>
          <DialogDescription>
            Update the ingress configuration and settings.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ingress Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter ingress name'
                      {...field}
                      disabled={updating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='class'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ingress Class</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={updating}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select ingress class' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='nginx'>nginx</SelectItem>
                      <SelectItem value='traefik'>traefik</SelectItem>
                      <SelectItem value='haproxy'>haproxy</SelectItem>
                      <SelectItem value='istio'>istio</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='status_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value))}
                    defaultValue={field.value?.toString()}
                    disabled={updating}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select status' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='1'>Active</SelectItem>
                      <SelectItem value='2'>Inactive</SelectItem>
                      <SelectItem value='3'>Pending</SelectItem>
                      <SelectItem value='4'>Error</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={updating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={updating}>
                {updating ? 'Updating...' : 'Update Ingress'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
