'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Network } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useIngressSpecStore } from '@/store/ingress-spec/action';

const createIngressSpecSchema = z.object({
  host: z.string().min(1, 'Host is required'),
  path: z.string().min(1, 'Path is required'),
  port: z
    .number()
    .min(1, 'Port must be at least 1')
    .max(65535, 'Port must be at most 65535'),
  service_id: z.number().min(1, 'Service is required'),
});

type CreateIngressSpecForm = z.infer<typeof createIngressSpecSchema>;

interface Service {
  id: number;
  name: string;
  port: string;
  type: string;
}

interface CreateIngressSpecModalProps {
  ingressId: number;
  ingressName: string;
  services: Service[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CreateIngressSpecModal({
  ingressId,
  ingressName,
  services,
  open,
  onOpenChange,
  onSuccess,
}: CreateIngressSpecModalProps) {
  const { createIngressSpec, creating } = useIngressSpecStore();

  const form = useForm<CreateIngressSpecForm>({
    resolver: zodResolver(createIngressSpecSchema),
    defaultValues: {
      host: '',
      path: '/',
      port: 80,
      service_id: 0,
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        host: '',
        path: '/',
        port: 80,
        service_id: 0,
      });
    }
  }, [open, form]);

  const onSubmit = async (data: CreateIngressSpecForm) => {
    try {
      const response = await createIngressSpec({
        ...data,
        ingress_id: ingressId,
      });

      if (response?.status) {
        toast.success('Ingress spec created successfully');
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to create ingress spec');
      }
    } catch (error) {
      console.error('Error creating ingress spec:', error);
      toast.error('Failed to create ingress spec');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Network className='h-5 w-5' />
            Add Ingress Spec
          </DialogTitle>
          <DialogDescription>
            {`Add a new route specification to "${ingressName}" ingress.`}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='host'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Host</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='example.com'
                      {...field}
                      disabled={creating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='path'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Path</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='/api/v1'
                      {...field}
                      disabled={creating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='port'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Port</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      placeholder='80'
                      {...field}
                      onChange={e =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                      disabled={creating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='service_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value))}
                    disabled={creating || services.length === 0}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            services.length === 0
                              ? 'No services available'
                              : 'Select a service'
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {services.map(service => (
                        <SelectItem
                          key={service.id}
                          value={service.id.toString()}
                        >
                          <div className='flex flex-col'>
                            <span className='font-medium'>{service.name}</span>
                            <span className='text-xs text-muted-foreground'>
                              Port: {service.port} • Type: {service.type}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                  {services.length === 0 && (
                    <p className='text-xs text-muted-foreground'>
                      No services available in this project. Create a service
                      first.
                    </p>
                  )}
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={creating}
              >
                Cancel
              </Button>
              <Button
                type='submit'
                disabled={creating || services.length === 0}
              >
                {creating ? 'Creating...' : 'Create Ingress Spec'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
