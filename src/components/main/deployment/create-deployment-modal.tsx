'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useDeploymentStore } from '@/store/deployment/action';

const createDeploymentSchema = z.object({
  name: z.string().min(1, 'Deployment name is required'),
  image: z.string().min(1, 'Image is required'),
  container_port: z.number().min(1, 'Container port must be at least 1'),
  replicas: z.number().min(1, 'Replicas must be at least 1'),
});

type CreateDeploymentForm = z.infer<typeof createDeploymentSchema>;

interface CreateDeploymentModalProps {
  namespaceId: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CreateDeploymentModal({
  namespaceId,
  open,
  onOpenChange,
  onSuccess,
}: CreateDeploymentModalProps) {
  const { createDeployment, creating } = useDeploymentStore();

  const form = useForm<CreateDeploymentForm>({
    resolver: zodResolver(createDeploymentSchema),
    defaultValues: {
      name: '',
      image: '',
      container_port: 80,
      replicas: 1,
    },
  });

  const onSubmit = async (data: CreateDeploymentForm) => {
    try {
      const response = await createDeployment({
        ...data,
        namespace_id: namespaceId,
      });

      if (response?.status) {
        toast.success('Deployment created successfully');
        form.reset();
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to create deployment');
      }
    } catch (error) {
      console.error('Error creating deployment:', error);
      toast.error('Failed to create deployment');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[525px]'>
        <DialogHeader>
          <DialogTitle>Create New Deployment</DialogTitle>
          <DialogDescription>
            Create a new deployment for this project. Fill in the required
            information below.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Deployment Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='web-admin'
                        {...field}
                        disabled={creating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='image'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Docker Image</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='nginx:1.21'
                        {...field}
                        disabled={creating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='container_port'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Container Port</FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='80'
                        {...field}
                        onChange={e =>
                          field.onChange(parseInt(e.target.value) || 0)
                        }
                        disabled={creating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='replicas'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Replicas</FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='1'
                        {...field}
                        onChange={e =>
                          field.onChange(parseInt(e.target.value) || 0)
                        }
                        disabled={creating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={creating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creating}>
                {creating ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Creating...
                  </>
                ) : (
                  'Create Deployment'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
