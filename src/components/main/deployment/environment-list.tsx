'use client';

import { Co<PERSON>, Edit, MoreVertical, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { ConfirmDeleteModal } from '@/components/confirm-delete-modal';
import { EditEnvironmentModal } from '@/components/main/deployment/edit-environment-modal';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useEnvironmentStore } from '@/store/environment/action';
import { EnvironmentType } from '@/store/environment/type';

interface EnvironmentListProps {
  environments: EnvironmentType[];
  deploymentId: number;
  onSuccess?: () => void;
}

export function EnvironmentList({
  environments,
  deploymentId,
  onSuccess,
}: EnvironmentListProps) {
  const { deleteEnvironment, deleting } = useEnvironmentStore();
  const [editingEnvironment, setEditingEnvironment] =
    useState<EnvironmentType | null>(null);
  const [deletingEnvironment, setDeletingEnvironment] =
    useState<EnvironmentType | null>(null);

  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard');
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleEdit = (environment: EnvironmentType) => {
    setEditingEnvironment(environment);
  };

  const handleDelete = (environment: EnvironmentType) => {
    setDeletingEnvironment(environment);
  };

  const confirmDelete = async () => {
    if (!deletingEnvironment) {
      return;
    }

    try {
      const response = await deleteEnvironment(deletingEnvironment.id);
      if (response?.status) {
        toast.success('Environment variable deleted successfully');
        setDeletingEnvironment(null);
        onSuccess?.();
      } else {
        toast.error('Failed to delete environment variable');
      }
    } catch (error) {
      console.error('Error deleting environment variable:', error);
      toast.error('Failed to delete environment variable');
    }
  };

  const handleEditSuccess = () => {
    setEditingEnvironment(null);
    onSuccess?.();
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength) + '...';
  };

  return (
    <>
      <div className='space-y-2'>
        {environments.map(env => (
          <div
            key={env.id}
            className='border rounded-lg p-3 bg-card hover:bg-muted/50 transition-colors'
          >
            <div className='flex items-start justify-between gap-3'>
              <div className='flex-1 min-w-0'>
                <div className='flex items-center gap-2 mb-1'>
                  <span className='font-medium text-sm font-mono text-blue-600'>
                    {env.name}
                  </span>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleCopyToClipboard(env.name)}
                    className='h-5 w-5 p-0'
                    title='Copy variable name'
                  >
                    <Copy className='h-3 w-3' />
                  </Button>
                </div>
                <div className='group relative'>
                  <div className='font-mono text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded break-all'>
                    <span title={env.value}>{truncateText(env.value)}</span>
                  </div>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleCopyToClipboard(env.value)}
                    className='absolute right-1 top-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity'
                    title='Copy variable value'
                  >
                    <Copy className='h-3 w-3' />
                  </Button>
                </div>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-6 w-6 p-0'
                    disabled={deleting}
                  >
                    <MoreVertical className='h-3 w-3' />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuItem
                    onClick={() => handleEdit(env)}
                    className='flex items-center gap-2'
                  >
                    <Edit className='h-3 w-3' />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDelete(env)}
                    className='flex items-center gap-2 text-destructive focus:text-destructive'
                  >
                    <Trash2 className='h-3 w-3' />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        ))}
      </div>

      {/* Edit Environment Modal */}
      {editingEnvironment && (
        <EditEnvironmentModal
          environment={editingEnvironment}
          deploymentId={deploymentId}
          open={!!editingEnvironment}
          onOpenChange={() => setEditingEnvironment(null)}
          onSuccess={handleEditSuccess}
        />
      )}

      {/* Delete Confirmation Modal */}
      {deletingEnvironment && (
        <ConfirmDeleteModal
          open={deletingEnvironment !== null}
          onOpenChange={open => {
            if (!open) {
              setDeletingEnvironment(null);
            }
          }}
          onConfirm={confirmDelete}
          title='Delete Environment Variable'
          description={`Are you sure you want to delete the environment variable "${deletingEnvironment.name}"? This action cannot be undone.`}
        />
      )}
    </>
  );
}
