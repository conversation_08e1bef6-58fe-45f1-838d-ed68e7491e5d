'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { updateDeploymentStatus } from '@/actions/deployment';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useDeploymentStore } from '@/store/deployment/action';

const editDeploymentSchema = z.object({
  name: z.string().min(1, 'Deployment name is required'),
  image: z.string().min(1, 'Image is required'),
  container_port: z.number().min(1, 'Container port must be at least 1'),
  replicas: z.number().min(1, 'Replicas must be at least 1'),
});

type EditDeploymentForm = z.infer<typeof editDeploymentSchema>;

interface EditDeploymentModalProps {
  deploymentId: number;
  onSuccess: () => void;
  onClose: () => void;
}

export function EditDeploymentModal({
  deploymentId,
  onSuccess,
  onClose,
}: EditDeploymentModalProps) {
  const {
    selectedDeployment,
    loading,
    updating,
    fetchDeployment,
    updateDeployment,
  } = useDeploymentStore();

  const form = useForm<EditDeploymentForm>({
    resolver: zodResolver(editDeploymentSchema),
    defaultValues: {
      name: '',
      image: '',
      container_port: 80,
      replicas: 1,
    },
  });

  // Fetch deployment data when modal opens
  useEffect(() => {
    if (deploymentId) {
      fetchDeployment(deploymentId);
    }
  }, [deploymentId, fetchDeployment]);

  // Populate form when deployment data is loaded
  useEffect(() => {
    if (selectedDeployment) {
      form.reset({
        name: selectedDeployment.name,
        image: selectedDeployment.image,
        container_port: selectedDeployment.container_port,
        replicas: selectedDeployment.replicas,
      });
    }
  }, [selectedDeployment, form]);

  const onSubmit = async (data: EditDeploymentForm) => {
    try {
      // Use initial data from fetched deployment for namespace_id and status_id
      // Auto change status: if status id != 1, set status edit to 6
      const statusId = selectedDeployment?.status.id !== 1 ? 6 : 1;

      const updateData = {
        ...data,
        namespace_id: selectedDeployment?.namespace.id || 0,
        status_id: statusId,
      };
      await updateDeployment(deploymentId, updateData);

      // Update deployment status to 6 after successful edit
      await updateDeploymentStatus(deploymentId, { status_id: 6 });

      onSuccess();
    } catch (error) {
      console.error('Failed to update deployment:', error);
      // You can add error handling here (e.g., show toast notification)
    }
  };

  const isOpen = deploymentId !== null;

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className='sm:max-w-[525px]'>
        <DialogHeader>
          <DialogTitle>Edit Deployment</DialogTitle>
          <DialogDescription>
            Update the deployment configuration. Changes will be applied
            immediately.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className='flex items-center justify-center py-8'>
            <Loader2 className='h-6 w-6 animate-spin' />
            <span className='ml-2'>Loading deployment data...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Deployment Name</FormLabel>
                      <FormControl>
                        <Input placeholder='web-app' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='image'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Docker Image</FormLabel>
                      <FormControl>
                        <Input placeholder='nginx:latest' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='container_port'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Container Port</FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          placeholder='80'
                          {...field}
                          onChange={e =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='replicas'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Replicas</FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          placeholder='3'
                          {...field}
                          onChange={e =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type='button'
                  variant='outline'
                  onClick={onClose}
                  disabled={updating}
                >
                  Cancel
                </Button>
                <Button type='submit' disabled={updating}>
                  {updating ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Updating...
                    </>
                  ) : (
                    'Update Deployment'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
