'use client';

import { Al<PERSON><PERSON>riangle, Loader2 } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  ModalResponsive,
  ModalResponsiveClose,
  ModalResponsiveContent,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';

interface ConfirmDeleteModalProps {
  trigger?: React.ReactNode;
  title?: string;
  description?: string;
  itemName?: string;
  onConfirm: () => Promise<void>;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ConfirmDeleteModal({
  trigger,
  title = 'Confirm Delete',
  description,
  itemName,
  onConfirm,
  open,
  onOpenChange,
}: ConfirmDeleteModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsDeleting(true);
      await onConfirm();
      onOpenChange?.(false);
    } catch (error) {
      console.error('Error during deletion:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const defaultDescription = itemName
    ? `Are you sure you want to delete "${itemName}"? This action cannot be undone.`
    : 'Are you sure you want to delete this item? This action cannot be undone.';

  return (
    <ModalResponsive open={open} onOpenChange={onOpenChange}>
      {trigger && (
        <ModalResponsiveTrigger asChild>{trigger}</ModalResponsiveTrigger>
      )}
      <ModalResponsiveContent className='sm:max-w-md'>
        <ModalResponsiveHeader>
          <div className='flex items-center gap-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10'>
              <AlertTriangle className='h-5 w-5 text-destructive' />
            </div>
            <div>
              <ModalResponsiveTitle className='text-left'>
                {title}
              </ModalResponsiveTitle>
            </div>
          </div>
        </ModalResponsiveHeader>

        <ModalResponsiveDescription className='text-left text-muted-foreground'>
          {description || defaultDescription}
        </ModalResponsiveDescription>

        <ModalResponsiveFooter className='flex flex-col-reverse gap-3 sm:flex-row sm:justify-end sm:gap-2'>
          <ModalResponsiveClose asChild>
            <Button variant='outline' disabled={isDeleting}>
              Cancel
            </Button>
          </ModalResponsiveClose>
          <Button
            variant='destructive'
            onClick={handleConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin' />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </ModalResponsiveFooter>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
