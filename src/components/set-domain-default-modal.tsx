'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Star } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import type { DomainType } from '@/store/domain/type';

const setDomainDefaultSchema = z.object({
  is_redirect: z.boolean(),
});

type SetDomainDefaultFormData = z.infer<typeof setDomainDefaultSchema>;

interface SetDomainDefaultModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  domain: DomainType | null;
  onConfirm: (domain: DomainType, data: { is_redirect: boolean }) => void;
  isUpdating: boolean;
}

export function SetDomainDefaultModal({
  open,
  onOpenChange,
  domain,
  onConfirm,
  isUpdating,
}: SetDomainDefaultModalProps) {
  const form = useForm<SetDomainDefaultFormData>({
    resolver: zodResolver(setDomainDefaultSchema),
    defaultValues: {
      is_redirect: true,
    },
  });

  const handleSubmit = (data: SetDomainDefaultFormData) => {
    if (domain) {
      onConfirm(domain, data);
    }
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Star className='h-5 w-5' />
            Set Domain as Default
          </DialogTitle>
          <DialogDescription>
            Configure how to set &quot;{domain?.name}&quot; as the default
            domain for this project.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4'
          >
            <FormField
              control={form.control}
              name='is_redirect'
              render={({ field }) => (
                <FormItem className='space-y-3'>
                  <FormLabel>Redirect Configuration</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={value => field.onChange(value === 'true')}
                      value={field.value ? 'true' : 'false'}
                      className='flex flex-col space-y-2'
                    >
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='true' id='redirect-true' />
                        <label
                          htmlFor='redirect-true'
                          className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                        >
                          Enable redirect
                        </label>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='false' id='redirect-false' />
                        <label
                          htmlFor='redirect-false'
                          className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                        >
                          Disable redirect
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className='flex gap-2'>
              <Button
                type='button'
                variant='outline'
                onClick={handleCancel}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isUpdating}>
                {isUpdating ? 'Setting as Default...' : 'Set as Default'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
