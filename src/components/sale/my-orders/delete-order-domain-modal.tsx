'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  ModalResponsive,
  ModalResponsiveClose,
  ModalResponsiveContent,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';
import { useOrderStore } from '@/store/order/action';

interface DeleteOrderDomainModalProps {
  children: React.ReactNode;
  domainId: number;
  domainName: string;
  isAvailable: boolean;
  onSuccess?: () => void;
}

export function DeleteOrderDomainModal({
  children,
  domainId,
  domainName,
  isAvailable,
  onSuccess,
}: DeleteOrderDomainModalProps) {
  const [open, setOpen] = useState(false);
  const { deleteOrderDomain, deletingOrderDomain } = useOrderStore();

  const handleDelete = async () => {
    // Check if domain is available before deletion
    if (isAvailable) {
      toast.error(
        'Cannot delete available domains. Please mark the domain as unavailable first.'
      );
      return;
    }

    try {
      const response = await deleteOrderDomain(domainId);

      if (response?.status) {
        toast.success('Order domain deleted successfully');
        setOpen(false);
        onSuccess?.();
      } else {
        toast.error(response?.message || 'Failed to delete order domain');
      }
    } catch (error: any) {
      console.error('Failed to delete order domain:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to delete order domain'
      );
    }
  };

  return (
    <ModalResponsive open={open} onOpenChange={setOpen}>
      <ModalResponsiveTrigger asChild>{children}</ModalResponsiveTrigger>
      <ModalResponsiveContent className='max-w-md'>
        <ModalResponsiveHeader>
          <ModalResponsiveTitle className='flex items-center gap-2'>
            <AlertTriangle className='h-5 w-5 text-destructive' />
            Delete Order Domain
          </ModalResponsiveTitle>
          <ModalResponsiveDescription>
            {isAvailable ? (
              <>
                {`Cannot delete the domain "${domainName}" because it is marked as
                available. Please mark the domain as unavailable first before
                deleting.`}
              </>
            ) : (
              <>
                {`Are you sure you want to delete the domain "${domainName}"? This
                action cannot be undone.`}
              </>
            )}
          </ModalResponsiveDescription>
        </ModalResponsiveHeader>

        <ModalResponsiveFooter className='flex flex-col-reverse gap-3 sm:flex-row sm:justify-end sm:gap-2'>
          <ModalResponsiveClose asChild>
            <Button variant='outline' disabled={deletingOrderDomain}>
              Cancel
            </Button>
          </ModalResponsiveClose>
          <Button
            variant='destructive'
            onClick={handleDelete}
            disabled={deletingOrderDomain || isAvailable}
          >
            {deletingOrderDomain ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin' />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className='h-4 w-4' />
                {isAvailable ? 'Cannot Delete' : 'Delete Domain'}
              </>
            )}
          </Button>
        </ModalResponsiveFooter>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
