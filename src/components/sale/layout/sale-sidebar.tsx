'use client';

import * as React from 'react';

import { NavMain } from '@/components/main/layout/nav-main';
import { NavSecondary } from '@/components/main/layout/nav-secondary';
import { NavUser } from '@/components/main/layout/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
} from '@/components/ui/sidebar';
import { salesNavigationData } from '@/lib/sales-navigation';
import { useAuthStore } from '@/store/auth/action';

export function SaleSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { me } = useAuthStore();

  // Create user object for NavUser component
  const currentUser = me
    ? {
        name: me.name,
        email: me.email,
        avatar: '/avatars/blank-profile.webp',
      }
    : salesNavigationData.user; // Fallback to sales user data

  return (
    <Sidebar variant='inset' {...props}>
      <SidebarContent>
        <NavMain items={salesNavigationData.navMain} />
        {/*<NavProjects projects={salesNavigationData.projects} />*/}
        <NavSecondary
          items={salesNavigationData.navSecondary}
          className='mt-auto'
        />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={currentUser} />
      </SidebarFooter>
    </Sidebar>
  );
}
