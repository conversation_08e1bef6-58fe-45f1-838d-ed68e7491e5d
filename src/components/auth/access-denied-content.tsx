'use client';

import { Shield<PERSON>, ArrowLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ComponentProps } from 'react';

import { Button } from '@/components/ui/button';

import { AuthCard } from './auth-card';

export function AccessDeniedContent({
  className,
  ...props
}: ComponentProps<'div'>) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get('redirect');

  const handleGoToAllowedRoute = () => {
    // Redirect to root and let middleware handle the proper redirect based on user role
    router.push('/');
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <AuthCard className={className} {...props}>
      <div className='flex flex-col items-center text-center space-y-4'>
        <div className='flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20'>
          <ShieldX className='h-8 w-8 text-red-600' />
        </div>

        <div className='space-y-2'>
          <h1 className='text-2xl font-medium'>Access Denied</h1>
          <p className='text-muted-foreground text-balance'>
            You don not have permission to access this page.
            {redirectUrl && (
              <span className='block mt-2 text-sm'>
                Requested:{' '}
                <code className='bg-muted px-1 py-0.5 rounded text-xs'>
                  {redirectUrl}
                </code>
              </span>
            )}
          </p>
        </div>

        <div className='flex flex-col sm:flex-row gap-3 w-full'>
          <Button onClick={handleGoToAllowedRoute} className='flex-1'>
            Go to My Dashboard
          </Button>

          <Button variant='outline' onClick={handleGoBack} className='flex-1'>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Go Back
          </Button>
        </div>
      </div>

      <div className='text-center text-sm text-muted-foreground'>
        If you believe this is an error, please contact your administrator.
      </div>
    </AuthCard>
  );
}
