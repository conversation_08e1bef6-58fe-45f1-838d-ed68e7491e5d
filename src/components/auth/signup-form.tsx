import { ComponentProps } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { AuthCard } from './auth-card';
import { SocialLoginButtons } from './social-login-buttons';

export function SignupForm({ className, ...props }: ComponentProps<'div'>) {
  return (
    <AuthCard className={className} {...props}>
      <div className='flex flex-col items-center text-center'>
        <h1 className='text-2xl font-medium'>Create an account</h1>
        <p className='text-muted-foreground text-balance'>
          Join Acme Inc and get started today
        </p>
      </div>
      <div className='grid gap-3'>
        <Label htmlFor='name'>Full Name</Label>
        <Input id='name' type='text' placeholder='<PERSON>' required />
      </div>
      <div className='grid gap-3'>
        <Label htmlFor='email'>Email</Label>
        <Input id='email' type='email' placeholder='<EMAIL>' required />
      </div>
      <div className='grid gap-3'>
        <Label htmlFor='password'>Password</Label>
        <Input id='password' type='password' required />
      </div>
      <div className='grid gap-3'>
        <Label htmlFor='confirmPassword'>Confirm Password</Label>
        <Input id='confirmPassword' type='password' required />
      </div>
      <Button type='submit' className='w-full'>
        Create Account
      </Button>
      <SocialLoginButtons mode='signup' />
      <div className='text-center text-sm'>
        Already have an account?{' '}
        <a href='/signin' className='underline underline-offset-4'>
          Sign in
        </a>
      </div>
    </AuthCard>
  );
}
