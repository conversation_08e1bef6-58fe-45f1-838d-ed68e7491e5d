'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { setCookie } from 'cookies-next';
import { useRouter } from 'next/navigation';
import { ComponentProps, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { loginAction } from '@/actions/user';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { AuthCard } from './auth-card';

const signinSchema = z.object({
  email: z
    .string()
    .min(1, 'Username is required')
    .min(3, 'Username must be at least 3 characters'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

type SigninFormData = z.infer<typeof signinSchema>;

export function SigninForm({ className, ...props }: ComponentProps<'div'>) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  // const searchParams = useSearchParams();
  // const redirectUrl = searchParams.get('redirect') || '/';

  const form = useForm<SigninFormData>({
    resolver: zodResolver(signinSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: SigninFormData) => {
    setIsLoading(true);
    try {
      const result: any = await loginAction({
        email: data.email,
        password: data.password,
      });

      if (result.status) {
        await setCookie('access_token', result.data.token, {
          maxAge: 60 * 24 * 30,
        });
        // await router.push(redirectUrl);
        if (result.data.user.user_type.is_sale) {
          await router.push('/my-orders');
        } else {
          await router.push('/dashboard');
        }
        form.reset();
      } else {
        form.setError('root', {
          type: 'manual',
          message: 'Invalid username or password. Please try again.',
        });
      }
    } catch {
      form.setError('root', {
        type: 'manual',
        message: 'An error occurred. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthCard className={className} {...props}>
      <div className='flex flex-col items-center text-center space-y-2'>
        <div className='w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-4'>
          <svg
            className='w-8 h-8 text-white'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
            />
          </svg>
        </div>
        <h1 className='text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent'>
          Welcome Back
        </h1>
        <p className='text-gray-400 text-sm'>
          Please sign in to your account to continue
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-5'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-sm font-medium text-gray-300'>
                  Username
                </FormLabel>
                <FormControl>
                  <Input
                    type='text'
                    placeholder='Enter your username'
                    className='h-12 px-4 bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-colors'
                    {...field}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel className='text-sm font-medium text-gray-300'>
                  Password
                </FormLabel>
                <FormControl>
                  <Input
                    type='password'
                    placeholder='Enter your password'
                    className='h-12 px-4 bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-colors'
                    {...field}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {form.formState.errors.root && (
            <div className='bg-red-900/20 border border-red-800 rounded-lg p-3'>
              <div className='text-red-400 text-sm text-center'>
                {form.formState.errors.root.message}
              </div>
            </div>
          )}

          <Button
            type='submit'
            className='w-full h-12 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg'
            disabled={isLoading}
          >
            {isLoading ? (
              <div className='flex items-center justify-center space-x-2'>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
                <span>Signing in...</span>
              </div>
            ) : (
              'Sign In'
            )}
          </Button>
        </form>
      </Form>

      {/*<SocialLoginButtons mode='signin' />*/}
      {/*<div className='text-center text-sm'>*/}
      {/*  Don&apos;t have an account?{' '}*/}
      {/*  <a href='/signup' className='underline underline-offset-4'>*/}
      {/*    Sign up*/}
      {/*  </a>*/}
      {/*</div>*/}
    </AuthCard>
  );
}
