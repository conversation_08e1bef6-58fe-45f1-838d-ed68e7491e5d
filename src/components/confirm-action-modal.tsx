'use client';

import { CheckCir<PERSON>, Loader2 } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  ModalResponsive,
  ModalResponsiveClose,
  ModalResponsiveContent,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';

interface ConfirmActionModalProps {
  trigger?: React.ReactNode;
  title?: string;
  description?: string;
  actionText?: string;
  loadingText?: string;
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  icon?: React.ReactNode;
  onConfirm: () => Promise<void>;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ConfirmActionModal({
  trigger,
  title = 'Confirm Action',
  description = 'Are you sure you want to perform this action?',
  actionText = 'Confirm',
  loadingText = 'Processing...',
  variant = 'default',
  icon = <CheckCircle className='h-5 w-5 text-green-600' />,
  onConfirm,
  open,
  onOpenChange,
}: ConfirmActionModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsProcessing(true);
      await onConfirm();
      onOpenChange?.(false);
    } catch (error) {
      console.error('Error during action:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ModalResponsive open={open} onOpenChange={onOpenChange}>
      {trigger && (
        <ModalResponsiveTrigger asChild>{trigger}</ModalResponsiveTrigger>
      )}
      <ModalResponsiveContent className='sm:max-w-md'>
        <ModalResponsiveHeader>
          <div className='flex items-center gap-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
              {icon}
            </div>
            <div>
              <ModalResponsiveTitle className='text-left'>
                {title}
              </ModalResponsiveTitle>
            </div>
          </div>
        </ModalResponsiveHeader>

        <ModalResponsiveDescription className='text-left text-muted-foreground'>
          {description}
        </ModalResponsiveDescription>

        <ModalResponsiveFooter className='flex flex-col-reverse gap-3 sm:flex-row sm:justify-end sm:gap-2'>
          <ModalResponsiveClose asChild>
            <Button variant='outline' disabled={isProcessing}>
              Cancel
            </Button>
          </ModalResponsiveClose>
          <Button
            variant={variant}
            onClick={handleConfirm}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin' />
                {loadingText}
              </>
            ) : (
              actionText
            )}
          </Button>
        </ModalResponsiveFooter>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
