'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, X } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  ModalResponsive,
  ModalResponsiveClose,
  ModalResponsiveContent,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
} from '@/components/ui/modal-responsive';
import type { DomainType } from '@/store/domain/type';

interface ConfirmDeleteDomainModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  domain: DomainType | null;
  onConfirm: (domain: DomainType) => Promise<void>;
  isDeleting?: boolean;
}

export function ConfirmDeleteDomainModal({
  open,
  onOpenChange,
  domain,
  onConfirm,
  isDeleting = false,
}: ConfirmDeleteDomainModalProps) {
  const [confirmText, setConfirmText] = useState('');
  const expectedText = domain?.name || '';

  const handleConfirm = async () => {
    if (domain && confirmText === expectedText) {
      await onConfirm(domain);
      setConfirmText('');
      onOpenChange(false);
    }
  };

  const handleClose = () => {
    setConfirmText('');
    onOpenChange(false);
  };

  const isConfirmDisabled = confirmText !== expectedText || isDeleting;

  if (!domain) {
    return null;
  }

  return (
    <ModalResponsive open={open} onOpenChange={onOpenChange}>
      <ModalResponsiveContent className='sm:max-w-lg'>
        <ModalResponsiveHeader>
          <div className='flex items-center gap-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10'>
              <AlertTriangle className='h-5 w-5 text-destructive' />
            </div>
            <div>
              <ModalResponsiveTitle className='text-destructive'>
                Delete Domain
              </ModalResponsiveTitle>
              <ModalResponsiveDescription>
                This action cannot be undone and will permanently remove the
                domain.
              </ModalResponsiveDescription>
            </div>
          </div>
        </ModalResponsiveHeader>

        <div className='space-y-4'>
          {/* Domain Information */}
          <div className='rounded-lg border p-4 bg-muted/50'>
            <div className='space-y-3'>
              <div>
                <label className='text-sm font-medium'>Domain Name</label>
                <div className='font-mono text-sm mt-1 p-2 bg-background rounded border'>
                  {domain.name}
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium'>Status</label>
                  <div className='mt-1'>
                    <Badge variant={domain.is_active ? 'default' : 'secondary'}>
                      {domain.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className='text-sm font-medium'>Type</label>
                  <div className='mt-1'>
                    {domain.is_default ? (
                      <Badge variant='default'>Default Domain</Badge>
                    ) : (
                      <Badge variant='outline'>Regular Domain</Badge>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <label className='text-sm font-medium'>Account</label>
                <div className='text-sm text-muted-foreground mt-1'>
                  {domain.account_name}
                </div>
              </div>
            </div>
          </div>

          {/* Warning Message */}
          <div className='rounded-lg border border-destructive/20 bg-destructive/5 p-4'>
            <div className='flex items-start gap-3'>
              <AlertTriangle className='h-5 w-5 text-destructive flex-shrink-0 mt-0.5' />
              <div className='space-y-2'>
                <h4 className='font-medium text-destructive'>Warning</h4>
                <div className='text-sm text-muted-foreground space-y-1'>
                  <p>Deleting this domain will:</p>
                  <ul className='list-disc list-inside space-y-1 ml-2'>
                    <li>Permanently remove the domain from your project</li>
                    <li>Remove all associated configurations</li>
                    <li>This action cannot be undone</li>
                    {domain.is_default && (
                      <li className='text-destructive font-medium'>
                        Remove the default domain designation
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className='space-y-2'>
            <label className='text-sm font-medium'>
              Type{' '}
              <span className='font-mono bg-muted px-1 rounded'>
                {domain.name}
              </span>{' '}
              to confirm deletion:
            </label>
            <input
              type='text'
              value={confirmText}
              onChange={e => setConfirmText(e.target.value)}
              placeholder={`Type "${domain.name}" here`}
              className='w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent'
              disabled={isDeleting}
            />
            {confirmText && confirmText !== expectedText && (
              <p className='text-sm text-destructive'>
                {`Domain name doesn't match. Please type "${domain.name}" exactly.`}
              </p>
            )}
          </div>
        </div>

        <ModalResponsiveFooter className='flex gap-2'>
          <ModalResponsiveClose asChild>
            <Button
              variant='outline'
              onClick={handleClose}
              disabled={isDeleting}
            >
              <X className='h-4 w-4 mr-2' />
              Cancel
            </Button>
          </ModalResponsiveClose>
          <Button
            variant='destructive'
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
          >
            {isDeleting ? (
              <>
                <div className='h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent' />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className='h-4 w-4 mr-2' />
                Delete Domain
              </>
            )}
          </Button>
        </ModalResponsiveFooter>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
