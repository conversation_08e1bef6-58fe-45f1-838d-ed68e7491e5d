'use client';
import { motion } from 'motion/react';
import Image from 'next/image';
import React, { useRef } from 'react';

const transition = {
  type: 'spring' as const,
  mass: 0.5,
  damping: 11.5,
  stiffness: 100,
  restDelta: 0.001,
  restSpeed: 0.001,
};

export const MenuItem = ({
  setActive,
  active,
  item,
  children,
  href,
}: {
  setActive: (item: string) => void;
  active: string | null;
  item: string;
  children?: React.ReactNode;
  href?: string;
}) => {
  const handleClick = () => {
    if (href) {
      window.location.href = href;
    }
  };

  const content = (
    <motion.p
      transition={{ duration: 0.3 }}
      className='cursor-pointer text-black hover:opacity-[0.9] dark:text-white px-2 py-2'
      onClick={handleClick}
    >
      {item}
    </motion.p>
  );

  return (
    <div onMouseEnter={() => setActive(item)} className='relative '>
      {href ? (
        <a href={href} className='block'>
          {content}
        </a>
      ) : (
        content
      )}
      {active !== null && children && (
        <motion.div
          initial={{ opacity: 0, scale: 0.85, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={transition}
        >
          {active === item && (
            <>
              {/* Invisible bridge to connect menu item with popover */}
              <div className='absolute top-full left-1/2 transform -translate-x-1/2 w-full h-2 bg-transparent' />

              <div className='absolute top-[calc(100%_+_0.5rem)] left-1/2 transform -translate-x-1/2 pt-1'>
                <motion.div
                  transition={transition}
                  layoutId='active' // layoutId ensures smooth animation
                  className='bg-white dark:bg-black backdrop-blur-sm rounded-2xl overflow-hidden border border-black/[0.2] dark:border-white/[0.2] shadow-xl'
                >
                  <motion.div
                    layout // layout ensures smooth animation
                    className='w-max h-full p-2'
                  >
                    {children}
                  </motion.div>
                </motion.div>
              </div>
            </>
          )}
        </motion.div>
      )}
    </div>
  );
};

export const Menu = ({
  setActive,
  children,
}: {
  setActive: (item: string | null) => void;
  children: React.ReactNode;
}) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleMouseLeave = () => {
    // Add a small delay before closing the popover
    timeoutRef.current = setTimeout(() => {
      setActive(null);
    }, 150); // 150ms delay
  };

  const handleMouseEnter = () => {
    // Clear timeout if user re-enters the menu area
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  return (
    <nav
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
      className='relative bg-transparent flex justify-center space-x-4'
    >
      {children}
    </nav>
  );
};

export const ProductItem = ({
  title,
  description,
  href,
  src,
}: {
  title: string;
  description: string;
  href: string;
  src: string;
}) => {
  return (
    <a
      href={href}
      className='flex space-x-2 group cursor-pointer relative p-2 rounded-lg transition-all duration-300 hover:bg-gray-50 dark:hover:bg-neutral-800/50'
    >
      {/* Image Container */}
      <div className='relative overflow-hidden rounded-lg shrink-0'>
        <Image
          src={src}
          width={120}
          height={80}
          alt={title}
          className='rounded-lg shadow-lg'
        />
      </div>

      {/* Content Container */}
      <div className='flex-1 min-w-0'>
        <h4 className='text-sm font-medium mb-1 text-black dark:text-white'>
          {title}
        </h4>
        <p className='text-neutral-700 text-xs max-w-[10rem] dark:text-neutral-300 leading-relaxed'>
          {description}
        </p>
      </div>
    </a>
  );
};

export const HoveredLink = ({
  children,
  ...rest
}: React.ComponentProps<'a'>) => {
  return (
    <a
      {...rest}
      className='text-neutral-700 dark:text-neutral-200 hover:text-black '
    >
      {children}
    </a>
  );
};
