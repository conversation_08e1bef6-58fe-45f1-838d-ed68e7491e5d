'use client';

import * as React from 'react';

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Sheet,
  SheetClose,
  SheetContent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>ooter,
  Sheet<PERSON>eader,
  She<PERSON><PERSON>itle,
  Sheet<PERSON>rigger,
} from '@/components/ui/sheet';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface ModalResponsiveProps {
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface ModalResponsiveTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
  className?: string;
}

interface ModalResponsiveContentProps {
  children: React.ReactNode;
  className?: string;
  showCloseButton?: boolean;
  side?: 'top' | 'right' | 'bottom' | 'left';
}

interface ModalResponsiveHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface ModalResponsiveFooterProps {
  children: React.ReactNode;
  className?: string;
}

interface ModalResponsiveTitleProps {
  children: React.ReactNode;
  className?: string;
}

interface ModalResponsiveDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

interface ModalResponsiveCloseProps {
  children?: React.ReactNode;
  className?: string;
  asChild?: boolean;
}

function ModalResponsive({ children, ...props }: ModalResponsiveProps) {
  const isMobile = useIsMobile();

  // Always render Dialog during SSR and until we know the screen size
  if (isMobile === undefined) {
    return <Dialog {...props}>{children}</Dialog>;
  }

  if (isMobile) {
    return <Sheet {...props}>{children}</Sheet>;
  }

  return <Dialog {...props}>{children}</Dialog>;
}

function ModalResponsiveTrigger({
  children,
  className,
  asChild,
  ...props
}: ModalResponsiveTriggerProps) {
  const isMobile = useIsMobile();

  // Always render DialogTrigger during SSR and until we know the screen size
  if (isMobile === undefined) {
    return (
      <DialogTrigger className={className} asChild={asChild} {...props}>
        {children}
      </DialogTrigger>
    );
  }

  if (isMobile) {
    return (
      <SheetTrigger className={className} asChild={asChild} {...props}>
        {children}
      </SheetTrigger>
    );
  }

  return (
    <DialogTrigger className={className} asChild={asChild} {...props}>
      {children}
    </DialogTrigger>
  );
}

function ModalResponsiveContent({
  children,
  className,
  showCloseButton = true,
  side = 'bottom',
  ...props
}: ModalResponsiveContentProps) {
  const isMobile = useIsMobile();

  // Always render DialogContent during SSR and until we know the screen size
  if (isMobile === undefined) {
    return (
      <DialogContent
        className={cn(
          'max-w-[calc(100%-1rem)] w-full sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto',
          className
        )}
        showCloseButton={showCloseButton}
        {...props}
      >
        {children}
      </DialogContent>
    );
  }

  if (isMobile) {
    return (
      <SheetContent
        side={side}
        className={cn(
          'flex flex-col gap-0 max-h-[90vh] overflow-y-auto p-0 w-full',
          side === 'bottom' && 'inset-x-0',
          className
        )}
        {...props}
      >
        <div className='flex flex-col p-4 flex-1'>{children}</div>
      </SheetContent>
    );
  }

  return (
    <DialogContent
      className={cn(
        'max-w-[calc(100%-1rem)] w-full sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto',
        className
      )}
      showCloseButton={showCloseButton}
      {...props}
    >
      {children}
    </DialogContent>
  );
}

function ModalResponsiveHeader({
  children,
  className,
  ...props
}: ModalResponsiveHeaderProps) {
  const isMobile = useIsMobile();

  // Always render DialogHeader during SSR and until we know the screen size
  if (isMobile === undefined) {
    return (
      <DialogHeader className={className} {...props}>
        {children}
      </DialogHeader>
    );
  }

  if (isMobile) {
    return (
      <SheetHeader className={cn('px-0 pb-6', className)} {...props}>
        {children}
      </SheetHeader>
    );
  }

  return (
    <DialogHeader className={className} {...props}>
      {children}
    </DialogHeader>
  );
}

function ModalResponsiveFooter({
  children,
  className,
  ...props
}: ModalResponsiveFooterProps) {
  const isMobile = useIsMobile();

  // Always render DialogFooter during SSR and until we know the screen size
  if (isMobile === undefined) {
    return (
      <DialogFooter className={className} {...props}>
        {children}
      </DialogFooter>
    );
  }

  if (isMobile) {
    return (
      <SheetFooter className={cn('px-0', className)} {...props}>
        {children}
      </SheetFooter>
    );
  }

  return (
    <DialogFooter className={className} {...props}>
      {children}
    </DialogFooter>
  );
}

function ModalResponsiveTitle({
  children,
  className,
  ...props
}: ModalResponsiveTitleProps) {
  const isMobile = useIsMobile();

  // Always render DialogTitle during SSR and until we know the screen size
  if (isMobile === undefined) {
    return (
      <DialogTitle className={className} {...props}>
        {children}
      </DialogTitle>
    );
  }

  if (isMobile) {
    return (
      <SheetTitle className={className} {...props}>
        {children}
      </SheetTitle>
    );
  }

  return (
    <DialogTitle className={className} {...props}>
      {children}
    </DialogTitle>
  );
}

function ModalResponsiveDescription({
  children,
  className,
  ...props
}: ModalResponsiveDescriptionProps) {
  const isMobile = useIsMobile();

  // Always render DialogDescription during SSR and until we know the screen size
  if (isMobile === undefined) {
    return (
      <DialogDescription className={className} {...props}>
        {children}
      </DialogDescription>
    );
  }

  if (isMobile) {
    return (
      <SheetDescription className={cn('pb-4', className)} {...props}>
        {children}
      </SheetDescription>
    );
  }

  return (
    <DialogDescription className={className} {...props}>
      {children}
    </DialogDescription>
  );
}

function ModalResponsiveClose({
  children,
  className,
  asChild,
  ...props
}: ModalResponsiveCloseProps) {
  const isMobile = useIsMobile();

  // Always render DialogClose during SSR and until we know the screen size
  if (isMobile === undefined) {
    return (
      <DialogClose className={className} asChild={asChild} {...props}>
        {children}
      </DialogClose>
    );
  }

  if (isMobile) {
    return (
      <SheetClose className={className} asChild={asChild} {...props}>
        {children}
      </SheetClose>
    );
  }

  return (
    <DialogClose className={className} asChild={asChild} {...props}>
      {children}
    </DialogClose>
  );
}

export {
  ModalResponsive,
  ModalResponsiveClose,
  ModalResponsiveContent,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveTrigger,
};
