'use client';

import { X, CheckCircle, AlertCircle } from 'lucide-react';
import { createContext, useContext, useState, useCallback } from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export interface Toast {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'success' | 'destructive';
  duration?: number;
}

interface ToastContextType {
  toasts: Toast[];
  toast: (toast: Omit<Toast, 'id'>) => string;
  dismiss: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

function ToastComponent({
  toast,
  onDismiss,
}: {
  toast: Toast;
  onDismiss: (id: string) => void;
}) {
  const getIcon = () => {
    switch (toast.variant) {
      case 'success':
        return <CheckCircle className='h-5 w-5 text-green-500' />;
      case 'destructive':
        return <AlertCircle className='h-5 w-5 text-red-500' />;
      default:
        return null;
    }
  };

  return (
    <div
      className={cn(
        'pointer-events-auto relative flex w-full items-start gap-3 overflow-hidden rounded-md border p-4 pr-8 shadow-lg transition-all animate-in slide-in-from-right-full',
        toast.variant === 'destructive'
          ? 'border-red-200 bg-red-50 text-red-900'
          : toast.variant === 'success'
            ? 'border-green-200 bg-green-50 text-green-900'
            : 'border bg-background text-foreground'
      )}
    >
      {getIcon()}
      <div className='grid gap-1 flex-1'>
        {toast.title && (
          <div className='text-sm font-semibold'>{toast.title}</div>
        )}
        {toast.description && (
          <div className='text-sm opacity-90'>{toast.description}</div>
        )}
      </div>
      <Button
        variant='ghost'
        size='sm'
        className='absolute right-2 top-2 h-6 w-6 p-0 hover:bg-transparent'
        onClick={() => onDismiss(toast.id)}
      >
        <X className='h-4 w-4' />
      </Button>
    </div>
  );
}

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = useCallback(
    ({
      title,
      description,
      variant = 'default',
      duration = 5000,
    }: Omit<Toast, 'id'>) => {
      const id = Math.random().toString(36).substr(2, 9);
      const newToast: Toast = { id, title, description, variant, duration };

      setToasts(prev => [...prev, newToast]);

      // Auto remove toast after duration
      setTimeout(() => {
        setToasts(prev => prev.filter(t => t.id !== id));
      }, duration);

      return id;
    },
    []
  );

  const dismiss = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, toast, dismiss }}>
      {children}
      {/* Toast Container */}
      <div className='fixed bottom-0 right-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]'>
        <div className='space-y-2'>
          {toasts.map(toast => (
            <ToastComponent key={toast.id} toast={toast} onDismiss={dismiss} />
          ))}
        </div>
      </div>
    </ToastContext.Provider>
  );
}
