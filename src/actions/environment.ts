'use server';

import Http from '@/lib/http';

export async function createEnvironment(data: {
  name: string;
  value: string;
  deployment_id: number;
}) {
  try {
    const response = await Http.post('/environments', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateEnvironment(
  id: number,
  data: {
    name: string;
    value: string;
    deployment_id: number;
  }
) {
  try {
    const response = await Http.put(`/environments/${id}`, data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteEnvironment(id: number) {
  try {
    const response = await Http.delete(`/environments/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
