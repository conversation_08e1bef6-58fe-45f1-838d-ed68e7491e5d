'use server';

import Http from '@/lib/http';

export async function createClusterOperation(payload: {
  cluster_id: number;
  method: string;
}) {
  try {
    const response: any = await Http.post('/operations/cluster', payload);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createOperation(payload: {
  cluster_id: number;
  namespace_id: number;
  method: string;
}) {
  try {
    const response: any = await Http.post('/operations', payload);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
