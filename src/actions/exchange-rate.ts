'use server';

import HttpExternal from '@/lib/httpExternal';
import {
  ExchangeRateApiResponse,
  THBRateResponse,
} from '@/store/exchange-rate/type';

const EXCHANGE_RATE_API_URL = 'https://api.exchangerate-api.com/v4/latest/USD';

export async function getTHBExchangeRate(): Promise<THBRateResponse> {
  try {
    const response = await HttpExternal.get<ExchangeRateApiResponse>(
      EXCHANGE_RATE_API_URL
    );
    const data = response.data;

    // Extract only THB rate from the response
    const thbRate = data.rates.THB;

    if (!thbRate) {
      throw new Error('THB rate not found in response');
    }

    return {
      base: data.base,
      date: data.date,
      thb_rate: thbRate,
      time_last_updated: data.time_last_updated,
    };
  } catch (error) {
    console.error('Failed to fetch THB exchange rate:', error);
    throw error;
  }
}
