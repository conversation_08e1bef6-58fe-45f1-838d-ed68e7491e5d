'use server';

import Http from '@/lib/http';

export interface CreateIngressSpecRequest {
  host: string;
  path: string;
  port: number;
  service_id: number;
  ingress_id: number;
}

export interface UpdateIngressSpecRequest {
  host: string;
  path: string;
  port: number;
}

export async function createIngressSpec(data: CreateIngressSpecRequest) {
  try {
    const response = await Http.post('/ingress-specs', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateIngressSpec(
  id: number,
  data: UpdateIngressSpecRequest
) {
  try {
    const response = await Http.put(`/ingress-specs/${id}`, data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteIngressSpec(id: number) {
  try {
    const response = await Http.delete(`/ingress-specs/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getIngressSpec(id: number) {
  try {
    const response = await Http.get(`/ingress-specs/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getIngressSpecs(filters?: {
  ingress_id?: number;
  service_id?: number;
  host?: string;
  path?: string;
}) {
  try {
    const searchParams = new URLSearchParams();

    if (filters?.ingress_id) {
      searchParams.append('ingress_id', filters.ingress_id.toString());
    }
    if (filters?.service_id) {
      searchParams.append('service_id', filters.service_id.toString());
    }
    if (filters?.host) {
      searchParams.append('host', filters.host);
    }
    if (filters?.path) {
      searchParams.append('path', filters.path);
    }

    const url = `/ingress-specs${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
