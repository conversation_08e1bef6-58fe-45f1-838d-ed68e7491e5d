'use server';

import Http from '@/lib/http';

export async function getWorkspaces() {
  try {
    const response = await Http.get('/workspaces');
    return response.data;
  } catch (error) {
    console.log(error);
  }
}

export async function createWorkspace(payload: {
  name: string;
  description: string;
}) {
  try {
    const response = await Http.post('/workspaces', payload);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
