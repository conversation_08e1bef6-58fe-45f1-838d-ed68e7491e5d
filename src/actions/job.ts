'use server';

import Http from '@/lib/http';

export interface JobQueryParams {
  my_jobs?: boolean;
  name?: string;
  job_status_id?: number;
  event_id?: number;
  event?: string;
  action?: string;
}

export async function getJobs(params?: JobQueryParams) {
  try {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }

    const url = queryParams.toString()
      ? `/jobs?${queryParams.toString()}`
      : '/jobs';

    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
