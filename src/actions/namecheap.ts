'use server';

import Http from '@/lib/http';

export interface NamecheapDomainCheckRequest {
  domains: string[];
}

export interface PriceInfo {
  Duration: string;
  DurationType: string;
  Price: string;
  PricingType: string;
  AdditionalCost: string;
  RegularPrice: string;
  RegularPriceType: string;
  RegularAdditionalCost: string;
  RegularAdditionalCostType: string;
  YourPrice: string;
  YourPriceType: string;
  YourAdditonalCost: string;
  YourAdditonalCostType: string;
  PromotionPrice: string;
  Currency: string;
}

export interface DomainCheckResult {
  Domain: string;
  Available: string;
  ErrorNo: string;
  Description: string;
  IsPremiumName: string;
  PremiumRegistrationPrice: string;
  PremiumRenewalPrice: string;
  PremiumRestorePrice: string;
  PremiumTransferPrice: string;
  IcannFee: string;
  EapFee: string;
  Price: PriceInfo[];
}

export interface NamecheapDomainCheckResponse {
  status: boolean;
  message: string;
  data: {
    DomainCheckResult: DomainCheckResult[];
  };
}

export async function checkDomains(
  data: NamecheapDomainCheckRequest
): Promise<NamecheapDomainCheckResponse> {
  try {
    const response: any = await Http.post('/namecheap/check', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
