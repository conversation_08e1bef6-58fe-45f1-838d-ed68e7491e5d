'use server';

import Http from '@/lib/http';

export async function getDeployment(id: number) {
  try {
    const response = await Http.get(`/deployments/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createDeployment(data: {
  name: string;
  image: string;
  container_port: number;
  replicas: number;
  namespace_id: number;
}) {
  try {
    const response = await Http.post('/deployments', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateDeployment(
  id: number,
  data: {
    name: string;
    image: string;
    container_port: number;
    namespace_id: number;
    replicas: number;
    status_id: number;
  }
) {
  try {
    const response = await Http.put(`/deployments/${id}`, data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateDeploymentStatus(
  id: number,
  data: { status_id: number }
) {
  try {
    const response = await Http.patch(`/deployments/${id}/status`, data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteDeployment(id: number) {
  try {
    const response = await Http.delete(`/deployments/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
