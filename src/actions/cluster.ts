'use server';

import Http from '@/lib/http';

export async function getClusters(workspace_id?: number) {
  try {
    const url = workspace_id
      ? `/clusters?workspace_id=${workspace_id}`
      : '/clusters';
    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
  }
}

export async function createCluster(payload: {
  name: string;
  region: string;
  pool_name: string;
  size: string;
  node_count: number;
  workspace_id: number;
}) {
  try {
    const response = await Http.post('/clusters', payload);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateCluster(
  id: number,
  payload: {
    name: string;
    region: string;
    pool_name: string;
    size: string;
    node_count: number;
    workspace_id: number;
    status_id: number;
  }
) {
  try {
    const response = await Http.put(`/clusters/${id}`, payload);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getCluster(id: number) {
  try {
    const response = await Http.get(`/clusters/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
