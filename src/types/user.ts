export interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  image_url: string;
  birth_date: string;
  phone_number: string;
  address: string;
  nationality: string;
  cover_url: string;
  user_type: {
    id: number;
    name: string;
    is_admin: boolean;
  };
  bio?: string;
  location?: string;
  website?: string;
  github?: string;
  linkedin?: string;
  twitter?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  last_login?: string;
}

export interface UserListResponse {
  status: boolean;
  message: string;
  data: {
    content: User[];
    pageable: {
      pageNumber: number;
      pageSize: number;
      sort: {
        empty: boolean;
        unsorted: boolean;
        sorted: boolean;
      };
      offset: number;
      paged: boolean;
      unpaged: boolean;
    };
    last: boolean;
    totalElements: number;
    totalPages: number;
    first: boolean;
    size: number;
    number: number;
    sort: {
      empty: boolean;
      unsorted: boolean;
      sorted: boolean;
    };
    numberOfElements: number;
    empty: boolean;
  };
}

export interface UserListParams {
  page?: number;
  size?: number;
  searchName?: string;
  desc?: boolean;
  showType?: number;
  user_type_id?: number;
  is_active?: boolean;
}
