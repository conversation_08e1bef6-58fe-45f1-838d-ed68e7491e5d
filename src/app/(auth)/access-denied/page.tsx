import type { Metada<PERSON> } from 'next';
import { Suspense } from 'react';

import { AccessDeniedContent } from '@/components/auth/access-denied-content';

export const metadata: Metadata = {
  title: 'Access Denied',
  description: 'You do not have permission to access this page.',
};

function AccessDeniedFallback() {
  return (
    <div className='flex items-center justify-center min-h-[400px]'>
      <div className='text-muted-foreground'>Loading...</div>
    </div>
  );
}

export default function AccessDeniedPage() {
  return (
    <Suspense fallback={<AccessDeniedFallback />}>
      <AccessDeniedContent />
    </Suspense>
  );
}
