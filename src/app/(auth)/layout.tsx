import type { Metadata } from 'next';
import { ReactNode } from 'react';

export const metadata: Metadata = {
  title: {
    template: '%s | Acme Inc',
    default: 'Authentication | Acme Inc',
  },
  description: 'Sign in to your account or create a new one to get started.',
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <div className='min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black'>
      <div className='absolute inset-0 bg-grid-pattern opacity-5'></div>
      <div className='relative z-10'>{children}</div>
    </div>
  );
}
