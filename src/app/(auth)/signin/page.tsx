import type { <PERSON>ada<PERSON> } from 'next';
import { Suspense } from 'react';

import { SigninForm } from '@/components/auth/signin-form';

export const metadata: Metadata = {
  title: 'Sign In',
  description: 'Sign in to your Acme Inc account to access your dashboard.',
};

function SigninFormFallback() {
  return (
    <div className='flex items-center justify-center min-h-[400px]'>
      <div className='text-muted-foreground'>Loading...</div>
    </div>
  );
}

export default function SigninPage() {
  return (
    <Suspense fallback={<SigninFormFallback />}>
      <SigninForm />
    </Suspense>
  );
}
