'use client';

import { ReactNode, useEffect } from 'react';

import { SaleSidebar } from '@/components/sale/layout/sale-sidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { useAuthStore } from '@/store/auth/action';

export default function SaleLayout({ children }: { children: ReactNode }) {
  const { fetchMe } = useAuthStore();

  useEffect(() => {
    fetchMe();
  }, [fetchMe]);

  return (
    <SidebarProvider>
      <SaleSidebar />
      <SidebarInset>
        <div className='flex flex-1 flex-col'>
          <div className='container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
            {children}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
