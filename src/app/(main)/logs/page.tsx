'use client';

import {
  ChevronDown,
  ChevronRight,
  Clock,
  User,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { Suspense, useEffect, useState } from 'react';

import { MainHeader } from '@/components/main/layout/main-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useJobStore } from '@/store/job/action';
import type { JobType } from '@/store/job/type';

function LogsContent() {
  const searchParams = useSearchParams();
  const { jobs, loading, fetchJobs } = useJobStore();
  const [expandedJobs, setExpandedJobs] = useState<Set<number>>(new Set());

  // Get query parameters
  const event = searchParams.get('event');
  const eventId = searchParams.get('event_id');

  useEffect(() => {
    // Fetch jobs with filters
    const params = {
      my_jobs: true,
      ...(eventId && { event_id: parseInt(eventId) }),
      ...(event && { event: event }),
    };

    fetchJobs(params);
  }, [fetchJobs, event, eventId]);

  const toggleJobExpansion = (jobId: number) => {
    const newExpanded = new Set(expandedJobs);
    if (newExpanded.has(jobId)) {
      newExpanded.delete(jobId);
    } else {
      newExpanded.add(jobId);
    }
    setExpandedJobs(newExpanded);
  };

  const getStatusIcon = (statusName: string) => {
    switch (statusName.toLowerCase()) {
      case 'successed':
      case 'success':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case 'failed':
      case 'error':
        return <XCircle className='h-4 w-4 text-red-500' />;
      case 'processing':
      case 'running':
        return <Activity className='h-4 w-4 text-blue-500 animate-spin' />;
      default:
        return <AlertCircle className='h-4 w-4 text-yellow-500' />;
    }
  };

  const getStatusBadgeVariant = (statusName: string) => {
    switch (statusName.toLowerCase()) {
      case 'successed':
      case 'success':
        return 'default';
      case 'failed':
      case 'error':
        return 'destructive';
      case 'processing':
      case 'running':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getActionBadgeVariant = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create':
        return 'default';
      case 'delete':
      case 'destroy':
        return 'destructive';
      case 'update':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const JobRowSkeleton = () => (
    <TableRow>
      <TableCell>
        <Skeleton className='h-4 w-4' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-4 w-32' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-4 w-24' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-4 w-16' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-4 w-20' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-4 w-24' />
      </TableCell>
      <TableCell>
        <Skeleton className='h-4 w-32' />
      </TableCell>
    </TableRow>
  );

  return (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      <div className='space-y-2'>
        <h1 className='text-3xl font-medium tracking-tight'>Job Logs</h1>
        <p className='text-muted-foreground'>
          View job execution logs{event ? ` for ${event} events` : ''}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Activity className='h-5 w-5' />
            Job Execution History
            {(event || eventId) && (
              <div className='flex gap-2 ml-auto'>
                {event && <Badge variant='outline'>Event: {event}</Badge>}
                {eventId && (
                  <Badge variant='outline'>Event ID: {eventId}</Badge>
                )}
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-12'></TableHead>
                  <TableHead>Job Name</TableHead>
                  <TableHead>Event</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Created At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  // Loading skeletons
                  Array.from({ length: 5 }).map((_, index) => (
                    <JobRowSkeleton key={index} />
                  ))
                ) : jobs.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      className='text-center py-8 text-muted-foreground'
                    >
                      No job logs found
                      {(event || eventId) && ' for the specified filters'}
                    </TableCell>
                  </TableRow>
                ) : (
                  jobs.map((job: JobType) => (
                    <React.Fragment key={job.id}>
                      <TableRow className='group'>
                        <TableCell>
                          <Button
                            variant='ghost'
                            size='sm'
                            className='h-6 w-6 p-0'
                            onClick={() => toggleJobExpansion(job.id)}
                          >
                            {expandedJobs.has(job.id) ? (
                              <ChevronDown className='h-4 w-4' />
                            ) : (
                              <ChevronRight className='h-4 w-4' />
                            )}
                          </Button>
                        </TableCell>
                        <TableCell className='font-medium'>
                          <div className='flex flex-col'>
                            <span>{job.name}</span>
                            {job.description && (
                              <span className='text-sm text-muted-foreground'>
                                {job.description}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant='outline'>{job.event}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getActionBadgeVariant(job.action)}>
                            {job.action}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className='flex items-center gap-2'>
                            {getStatusIcon(job.job_status?.name || 'unknown')}
                            <Badge
                              variant={getStatusBadgeVariant(
                                job.job_status?.name || 'unknown'
                              )}
                            >
                              {job.job_status?.name || 'Unknown'}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='flex items-center gap-2'>
                            <User className='h-4 w-4 text-muted-foreground' />
                            <div className='flex flex-col'>
                              <span className='text-sm'>
                                {job.user?.name || 'Unknown User'}
                              </span>
                              <span className='text-xs text-muted-foreground'>
                                {job.user?.email || 'No email'}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='flex items-center gap-2'>
                            <Clock className='h-4 w-4 text-muted-foreground' />
                            <span className='text-sm'>
                              {formatDate(job.created_at)}
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>

                      {expandedJobs.has(job.id) && (
                        <TableRow>
                          <TableCell colSpan={7} className='p-0'>
                            <div className='border-t bg-muted/20 p-4'>
                              <div className='space-y-3'>
                                <h4 className='font-semibold text-sm flex items-center gap-2'>
                                  <Activity className='h-4 w-4' />
                                  Job Execution Logs
                                </h4>
                                {job.job_logs &&
                                Array.isArray(job.job_logs) &&
                                job.job_logs.length > 0 ? (
                                  <div className='space-y-2'>
                                    {job.job_logs.map((log, index) => (
                                      <div
                                        key={log.id || `log-${index}`}
                                        className='flex items-start gap-3 p-3 rounded-lg border bg-background'
                                      >
                                        <div className='flex-shrink-0 w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium text-primary'>
                                          {index + 1}
                                        </div>
                                        <div className='flex-1 space-y-1'>
                                          <div className='font-medium text-sm'>
                                            {log.name || 'Unnamed Log'}
                                          </div>
                                          <div className='text-sm text-muted-foreground'>
                                            {log.description ||
                                              'No description available'}
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <div className='text-sm text-muted-foreground italic'>
                                    No execution logs available for this job
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function LogsPage() {
  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />

      {/* Main Content */}
      <Suspense
        fallback={
          <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
            <div className='space-y-2'>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='h-4 w-96' />
            </div>
            <Card>
              <CardHeader>
                <Skeleton className='h-6 w-64' />
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className='h-16 w-full' />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        }
      >
        <LogsContent />
      </Suspense>
    </div>
  );
}
