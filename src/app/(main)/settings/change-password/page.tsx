'use client';

import {
  <PERSON>,
  Eye,
  EyeOff,
  Shield,
  <PERSON>ert<PERSON>riangle,
  CheckCircle,
} from 'lucide-react';
import { useState } from 'react';

import { MainHeader } from '@/components/main/layout/main-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function ChangePasswordPage() {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />

      {/* Main Content */}
      <div className='flex flex-1 flex-col gap-6 p-4 pt-0'>
        <div className='space-y-2'>
          <h1 className='text-3xl font-medium tracking-tight flex items-center gap-2'>
            <Lock className='h-8 w-8' />
            Change Password
          </h1>
          <p className='text-muted-foreground'>
            Update your password to keep your account secure.
          </p>
        </div>

        <div className='grid gap-6 lg:grid-cols-3'>
          {/* Change Password Form */}
          <div className='lg:col-span-2 space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>Update Password</CardTitle>
                <CardDescription>
                  Enter your current password and choose a new secure password.
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* Current Password */}
                <div className='space-y-2'>
                  <Label htmlFor='currentPassword'>Current Password</Label>
                  <div className='relative'>
                    <Input
                      id='currentPassword'
                      type={showCurrentPassword ? 'text' : 'password'}
                      placeholder='Enter your current password'
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                      onClick={() =>
                        setShowCurrentPassword(!showCurrentPassword)
                      }
                    >
                      {showCurrentPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>

                {/* New Password */}
                <div className='space-y-2'>
                  <Label htmlFor='newPassword'>New Password</Label>
                  <div className='relative'>
                    <Input
                      id='newPassword'
                      type={showNewPassword ? 'text' : 'password'}
                      placeholder='Enter your new password'
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Confirm Password */}
                <div className='space-y-2'>
                  <Label htmlFor='confirmPassword'>Confirm New Password</Label>
                  <div className='relative'>
                    <Input
                      id='confirmPassword'
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder='Confirm your new password'
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                    >
                      {showConfirmPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>

                <div className='flex gap-2 pt-4'>
                  <Button>Update Password</Button>
                  <Button variant='outline'>Cancel</Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Security Guidelines */}
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Shield className='h-5 w-5' />
                  Security Guidelines
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  <div className='flex items-start gap-2'>
                    <CheckCircle className='h-4 w-4 text-green-500 mt-0.5' />
                    <div className='text-sm'>
                      <p className='font-medium'>At least 8 characters</p>
                      <p className='text-muted-foreground'>
                        Use a combination of letters, numbers, and symbols
                      </p>
                    </div>
                  </div>
                  <div className='flex items-start gap-2'>
                    <CheckCircle className='h-4 w-4 text-green-500 mt-0.5' />
                    <div className='text-sm'>
                      <p className='font-medium'>
                        Include uppercase and lowercase
                      </p>
                      <p className='text-muted-foreground'>
                        Mix capital and small letters for better security
                      </p>
                    </div>
                  </div>
                  <div className='flex items-start gap-2'>
                    <AlertTriangle className='h-4 w-4 text-yellow-500 mt-0.5' />
                    <div className='text-sm'>
                      <p className='font-medium'>Avoid common passwords</p>
                      <p className='text-muted-foreground'>
                        Don not use easily guessable information
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Password Strength</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  <div className='flex justify-between text-sm'>
                    <span>Current strength</span>
                    <Badge variant='secondary'>Medium</Badge>
                  </div>
                  <div className='w-full bg-muted rounded-full h-2'>
                    <div className='bg-yellow-500 h-2 rounded-full w-1/2'></div>
                  </div>
                  <p className='text-xs text-muted-foreground'>
                    Add numbers and symbols to improve strength
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
