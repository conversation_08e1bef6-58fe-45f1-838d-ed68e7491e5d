import { MainHeader } from '@/components/main/layout/main-header';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function SettingsPage() {
  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />

      {/* Main Content */}
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
        <div className='space-y-2'>
          <h1 className='text-3xl font-medium tracking-tight'>Settings</h1>
          <p className='text-muted-foreground'>
            Manage your application settings and preferences.
          </p>
        </div>

        <div className='grid gap-6 lg:grid-cols-3'>
          {/* Settings Cards */}
          <div className='lg:col-span-2 space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>Profile Settings</CardTitle>
                <CardDescription>
                  Update your personal information and preferences.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='grid grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium'>First Name</label>
                      <input
                        type='text'
                        className='w-full p-2 border rounded-md'
                        placeholder='John'
                      />
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium'>Last Name</label>
                      <input
                        type='text'
                        className='w-full p-2 border rounded-md'
                        placeholder='Doe'
                      />
                    </div>
                  </div>
                  <div className='space-y-2'>
                    <label className='text-sm font-medium'>Email</label>
                    <input
                      type='email'
                      className='w-full p-2 border rounded-md'
                      placeholder='<EMAIL>'
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure how you receive notifications.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <p className='font-medium'>Email Notifications</p>
                      <p className='text-sm text-muted-foreground'>
                        Receive notifications via email
                      </p>
                    </div>
                    <input type='checkbox' className='h-4 w-4' />
                  </div>
                  <div className='flex items-center justify-between'>
                    <div>
                      <p className='font-medium'>Push Notifications</p>
                      <p className='text-sm text-muted-foreground'>
                        Receive push notifications in browser
                      </p>
                    </div>
                    <input type='checkbox' className='h-4 w-4' />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  <button className='w-full text-left p-2 rounded-md hover:bg-muted transition-colors'>
                    Change Password
                  </button>
                  <button className='w-full text-left p-2 rounded-md hover:bg-muted transition-colors'>
                    Export Data
                  </button>
                  <button className='w-full text-left p-2 rounded-md hover:bg-muted transition-colors text-red-600'>
                    Delete Account
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
