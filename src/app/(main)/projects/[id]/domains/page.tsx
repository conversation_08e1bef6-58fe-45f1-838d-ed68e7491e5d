'use client';

import {
  ArrowLeft,
  Globe,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Star,
  Calendar,
  Building,
  Hash,
  MoreHorizontal,
  Trash2,
  Cloud,
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Toaster, toast } from 'sonner';

import { CloudflareZonesModal } from '@/components/cloudflare-zones-modal';
import { ConfirmDeleteDomainModal } from '@/components/confirm-delete-domain-modal';
import { MainHeader } from '@/components/main/layout/main-header';
import { SetDomainDefaultModal } from '@/components/set-domain-default-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDebounce } from '@/hooks/use-debounce';
import { useDomainStore } from '@/store/domain/action';
import type { DomainType } from '@/store/domain/type';
import { useProjectStore } from '@/store/project/action';

export default function ProjectDomainsPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = parseInt(params.id as string);

  const {
    domains,
    loading,
    updating,
    deleting,
    fetchDomains,
    setDomainAsDefault,
    deleteDomain,
  } = useDomainStore();

  const { selectedProject, fetchProject } = useProjectStore();

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | undefined>(
    undefined
  );
  const [isDefaultFilter, setIsDefaultFilter] = useState<boolean | undefined>(
    undefined
  );
  const [isClient, setIsClient] = useState(false);

  // Modal states
  const [isCloudflareModalOpen, setIsCloudflareModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [domainToDelete, setDomainToDelete] = useState<DomainType | null>(null);
  const [isSetDefaultModalOpen, setIsSetDefaultModalOpen] = useState(false);
  const [domainToSetDefault, setDomainToSetDefault] =
    useState<DomainType | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fetch project details and domains
  useEffect(() => {
    if (projectId) {
      fetchProject(projectId);
      fetchDomainsWithFilters();
    }
  }, [projectId, fetchProject]);

  // Fetch domains when filters change
  useEffect(() => {
    if (projectId) {
      fetchDomainsWithFilters();
    }
  }, [debouncedSearchTerm, isActiveFilter, isDefaultFilter, projectId]);

  const fetchDomainsWithFilters = () => {
    const filters: any = {
      namespace_id: projectId,
    };

    if (debouncedSearchTerm.trim()) {
      filters.name = debouncedSearchTerm.trim();
    }
    if (isActiveFilter !== undefined) {
      filters.is_active = isActiveFilter;
    }
    if (isDefaultFilter !== undefined) {
      filters.is_default = isDefaultFilter;
    }

    fetchDomains(filters);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setIsActiveFilter(undefined);
    setIsDefaultFilter(undefined);
  };

  const hasActiveFilters =
    searchTerm.trim() ||
    isActiveFilter !== undefined ||
    isDefaultFilter !== undefined;

  const handleSetAsDefault = (domain: DomainType) => {
    setDomainToSetDefault(domain);
    setIsSetDefaultModalOpen(true);
  };

  const handleConfirmSetAsDefault = async (
    domain: DomainType,
    data: { is_redirect: boolean }
  ) => {
    try {
      const response = await setDomainAsDefault(domain.id, data);
      if (response?.status) {
        toast.success(`Domain "${domain.name}" set as default successfully`);
        fetchDomainsWithFilters();
        setIsSetDefaultModalOpen(false);
        setDomainToSetDefault(null);
      } else {
        toast.error(response?.message || 'Failed to set domain as default');
      }
    } catch (error: any) {
      console.error('Error setting domain as default:', error);
      toast.error(error?.message || 'Failed to set domain as default');
    }
  };

  const handleDeleteDomain = (domain: DomainType) => {
    setDomainToDelete(domain);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDelete = async (domain: DomainType) => {
    try {
      const response = await deleteDomain(domain.id);
      if (response?.status) {
        toast.success(`Domain "${domain.name}" deleted successfully`);
        fetchDomainsWithFilters();
      } else {
        toast.error(response?.message || 'Failed to delete domain');
      }
    } catch (error: any) {
      console.error('Error deleting domain:', error);
      toast.error(error?.message || 'Failed to delete domain');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Loading skeleton for table rows
  const DomainRowSkeleton = () => (
    <TableRow>
      <TableCell className='min-w-[200px]'>
        <Skeleton className='h-4 w-48' />
      </TableCell>
      <TableCell className='w-[100px]'>
        <Skeleton className='h-6 w-16' />
      </TableCell>
      <TableCell className='w-[80px]'>
        <Skeleton className='h-6 w-16' />
      </TableCell>
      <TableCell className='min-w-[280px]'>
        <Skeleton className='h-4 w-64' />
      </TableCell>
      <TableCell className='min-w-[150px]'>
        <Skeleton className='h-4 w-24' />
      </TableCell>
      <TableCell className='w-[120px]'>
        <Skeleton className='h-4 w-20' />
      </TableCell>
      <TableCell className='min-w-[140px]'>
        <Skeleton className='h-4 w-24' />
      </TableCell>
      <TableCell className='w-12'>
        <Skeleton className='h-8 w-8' />
      </TableCell>
    </TableRow>
  );

  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='h-96 bg-muted animate-pulse rounded-md flex items-center justify-center'>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      <MainHeader />
      <Toaster />

      {/* Cloudflare Zones Modal */}
      <CloudflareZonesModal
        open={isCloudflareModalOpen}
        onOpenChange={setIsCloudflareModalOpen}
        projectId={projectId}
        onDomainCreated={fetchDomainsWithFilters}
      />

      {/* Confirm Delete Domain Modal */}
      <ConfirmDeleteDomainModal
        open={isDeleteModalOpen}
        onOpenChange={setIsDeleteModalOpen}
        domain={domainToDelete}
        onConfirm={handleConfirmDelete}
        isDeleting={deleting}
      />

      {/* Set Domain as Default Modal */}
      <SetDomainDefaultModal
        open={isSetDefaultModalOpen}
        onOpenChange={setIsSetDefaultModalOpen}
        domain={domainToSetDefault}
        onConfirm={handleConfirmSetAsDefault}
        isUpdating={updating}
      />

      <div className='flex flex-1 flex-col gap-6 p-4 pt-0'>
        {/* Header Section */}
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push(`/projects/${projectId}`)}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div className='flex-1'>
            <div className='flex items-center gap-3 mb-2'>
              <Globe className='h-8 w-8' />
              <h1 className='text-3xl font-medium tracking-tight'>Domains</h1>
              {selectedProject && (
                <Badge variant='outline'>{selectedProject.name}</Badge>
              )}
            </div>
            <p className='text-muted-foreground'>
              Manage domains for this project namespace
            </p>
          </div>
          <div className='flex gap-2'>
            <Button
              variant='outline'
              onClick={() => setIsCloudflareModalOpen(true)}
            >
              <Cloud className='h-4 w-4 mr-2' />
              View Cloudflare Zones
            </Button>
          </div>
        </div>

        {/* Filters Section */}
        <div className='flex flex-col lg:flex-row gap-4'>
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search domains...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>

          <div className='flex gap-2'>
            <Button
              variant={isActiveFilter === true ? 'default' : 'outline'}
              size='sm'
              onClick={() =>
                setIsActiveFilter(isActiveFilter === true ? undefined : true)
              }
            >
              Active Only
            </Button>
            <Button
              variant={isActiveFilter === false ? 'default' : 'outline'}
              size='sm'
              onClick={() =>
                setIsActiveFilter(isActiveFilter === false ? undefined : false)
              }
            >
              Inactive Only
            </Button>
            <Button
              variant={isDefaultFilter === true ? 'default' : 'outline'}
              size='sm'
              onClick={() =>
                setIsDefaultFilter(isDefaultFilter === true ? undefined : true)
              }
            >
              Default Only
            </Button>
          </div>

          {hasActiveFilters && (
            <Button variant='ghost' onClick={clearFilters}>
              <Filter className='h-4 w-4 mr-2' />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Domains Table */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Globe className='h-5 w-5' />
              Domains ({domains.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='rounded-md border overflow-x-auto'>
              <Table className='min-w-full'>
                <TableHeader>
                  <TableRow>
                    <TableHead className='min-w-[200px]'>Domain Name</TableHead>
                    <TableHead className='w-[100px]'>Status</TableHead>
                    <TableHead className='w-[80px]'>Default</TableHead>
                    <TableHead className='min-w-[280px]'>Zone ID</TableHead>
                    <TableHead className='min-w-[150px]'>Account</TableHead>
                    <TableHead className='w-[120px]'>Namespace</TableHead>
                    <TableHead className='min-w-[140px]'>Created</TableHead>
                    <TableHead className='w-12'></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    // Loading skeletons
                    Array.from({ length: 5 }).map((_, index) => (
                      <DomainRowSkeleton key={index} />
                    ))
                  ) : domains.length === 0 ? (
                    // Empty state
                    <TableRow>
                      <TableCell colSpan={8} className='text-center py-12'>
                        <div className='flex flex-col items-center gap-4'>
                          <Globe className='h-12 w-12 text-muted-foreground' />
                          <div>
                            <h3 className='text-lg font-semibold'>
                              No domains found
                            </h3>
                            <p className='text-muted-foreground'>
                              {hasActiveFilters
                                ? 'No domains match your current filters'
                                : 'Get started by adding your first domain'}
                            </p>
                          </div>
                          {!hasActiveFilters && (
                            <div className='flex gap-2'>
                              <Button
                                variant='outline'
                                onClick={() => setIsCloudflareModalOpen(true)}
                              >
                                <Cloud className='h-4 w-4 mr-2' />
                                View Cloudflare Zones
                              </Button>
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    // Domain rows

                    domains.map(domain => (
                      <TableRow key={domain.id}>
                        <TableCell className='min-w-[200px]'>
                          <div className='flex flex-col'>
                            <span
                              className='font-medium truncate max-w-[180px]'
                              title={domain.name}
                            >
                              {domain.name}
                            </span>
                            <span className='text-sm text-muted-foreground whitespace-nowrap'>
                              ID: {domain.id}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className='w-[100px]'>
                          <Badge
                            variant={domain.is_active ? 'default' : 'secondary'}
                            className='whitespace-nowrap'
                          >
                            {domain.is_active ? (
                              <CheckCircle className='h-3 w-3 mr-1' />
                            ) : (
                              <XCircle className='h-3 w-3 mr-1' />
                            )}
                            {domain.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell className='w-[80px]'>
                          {domain.is_default && (
                            <Badge
                              variant='default'
                              className='whitespace-nowrap'
                            >
                              <Star className='h-3 w-3 mr-1' />
                              Default
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className='min-w-[280px]'>
                          <div className='flex items-center gap-1'>
                            <Hash className='h-3 w-3 text-muted-foreground flex-shrink-0' />
                            <span
                              className='font-mono text-sm truncate max-w-[250px]'
                              title={domain.zone_id}
                            >
                              {domain.zone_id}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className='min-w-[150px]'>
                          <div className='flex items-center gap-1'>
                            <Building className='h-3 w-3 text-muted-foreground flex-shrink-0' />
                            <span
                              className='text-sm truncate max-w-[120px]'
                              title={domain.account_name}
                            >
                              {domain.account_name}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className='w-[120px]'>
                          <Badge
                            variant='outline'
                            className='whitespace-nowrap truncate max-w-[110px]'
                            title={domain.namespace.name}
                          >
                            {domain.namespace.name}
                          </Badge>
                        </TableCell>
                        <TableCell className='min-w-[140px]'>
                          <div className='flex items-center gap-1'>
                            <Calendar className='h-3 w-3 text-muted-foreground flex-shrink-0' />
                            <span className='text-sm whitespace-nowrap'>
                              {formatDate(domain.created_at)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className='w-12'>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' size='sm'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              {!domain.is_default && (
                                <DropdownMenuItem
                                  onClick={() => handleSetAsDefault(domain)}
                                  disabled={updating || loading}
                                >
                                  <Star className='h-4 w-4 mr-2' />
                                  {updating
                                    ? 'Setting as Default...'
                                    : 'Set as Default'}
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteDomain(domain)}
                                disabled={deleting || loading}
                                className='text-destructive'
                              >
                                <Trash2 className='h-4 w-4 mr-2' />
                                {deleting ? 'Deleting...' : 'Delete Domain'}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
