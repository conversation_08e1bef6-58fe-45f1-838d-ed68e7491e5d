'use client';

import { useEffect } from 'react';

import { MainHeader } from '@/components/main/layout/main-header';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useDashboardStore } from '@/store/dashboard';

export default function AdminPanel() {
  const { salesAmount, websAmount, loading, fetchDashboard } =
    useDashboardStore();

  useEffect(() => {
    fetchDashboard();
  }, [fetchDashboard]);

  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />

      {/* Main Content */}
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
        <div className='grid auto-rows-min gap-4 md:grid-cols-3'>
          <Card>
            <CardHeader className='pb-2'>
              <CardDescription>Sales Amount</CardDescription>
              <CardTitle className='text-4xl'>
                {loading ? '...' : salesAmount}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-xs text-muted-foreground'>
                Total sales count
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='pb-2'>
              <CardDescription>Webs Amount</CardDescription>
              <CardTitle className='text-4xl'>
                {loading ? '...' : websAmount}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-xs text-muted-foreground'>
                Total webs count
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='pb-2'>
              <CardDescription>Revenue</CardDescription>
              <CardTitle className='text-4xl'>฿0</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-xs text-muted-foreground'>Fixed stat</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
