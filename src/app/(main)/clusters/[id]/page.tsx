'use client';

import {
  ArrowLeft,
  Server,
  Database,
  Globe,
  Trash2,
  Users,
  Activity,
  ChevronDown,
  ChevronRight,
  Clock,
  Play,
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { ConfirmActionModal } from '@/components/confirm-action-modal';
import { MainHeader } from '@/components/main/layout/main-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { useClusterStore } from '@/store/cluster/action';
import { useOperationStore } from '@/store/operation/action';
import { calculateProjectStatus } from '@/utils/calculateProjectStatus';
import { getBadgeVariant } from '@/utils/getVariant';

export default function ClusterDetailPage() {
  const { selectedCluster, loading, fetchCluster } = useClusterStore();
  const { createClusterOperation, setConfirmLoading } = useOperationStore();
  const router = useRouter();
  const params = useParams();
  const clusterId = Number(params.id);
  const [expandedNamespaces, setExpandedNamespaces] = useState<Set<number>>(
    new Set()
  );
  const [openPublishConfirm, setOpenPublishConfirm] = useState(false);
  const [openDestroyConfirm, setOpenDestroyConfirm] = useState(false);

  useEffect(() => {
    if (clusterId) {
      fetchCluster(clusterId);
    }
  }, [clusterId, fetchCluster]);

  const handleBack = () => {
    router.push('/clusters');
  };

  const toggleNamespace = (namespaceId: number) => {
    setExpandedNamespaces(prev => {
      const newSet = new Set(prev);
      if (newSet.has(namespaceId)) {
        newSet.delete(namespaceId);
      } else {
        newSet.add(namespaceId);
      }
      return newSet;
    });
  };

  const handlePublish = async () => {
    if (!selectedCluster) {
      return;
    }

    try {
      setConfirmLoading(true);
      const success = await createClusterOperation({
        cluster_id: selectedCluster.id,
        method: 'apply',
      });

      if (success) {
        setTimeout(() => {
          fetchCluster(clusterId);
          setConfirmLoading(false);
          setOpenPublishConfirm(false);
        }, 10000);
      }
    } catch (error) {
      console.error('Failed to publish cluster:', error);
    }
  };

  const handleDestroy = async () => {
    if (!selectedCluster) {
      return;
    }

    try {
      setConfirmLoading(true);
      const success = await createClusterOperation({
        cluster_id: selectedCluster.id,
        method: 'destroy',
      });

      if (success) {
        setTimeout(() => {
          fetchCluster(clusterId);
        }, 10000);
      }
    } catch (error) {
      console.error('Failed to publish cluster:', error);
    } finally {
      setConfirmLoading(false);
    }
  };

  const handlePublishClick = () => {
    setOpenPublishConfirm(true);
  };

  // Loading skeleton component
  const DetailSkeleton = () => (
    <div className='space-y-6'>
      <div className='flex items-center gap-4'>
        <Skeleton className='h-8 w-8' />
        <Skeleton className='h-8 w-48' />
      </div>
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className='h-4 w-32' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-20 w-full' />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <DetailSkeleton />
        </div>
      </div>
    );
  }

  if (!selectedCluster) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='flex flex-col items-center justify-center py-12 text-center'>
            <Server className='h-12 w-12 text-muted-foreground mb-4' />
            <h3 className='text-lg font-semibold'>Cluster not found</h3>
            <p className='text-muted-foreground'>
              The requested cluster could not be found
            </p>
            <Button onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to Clusters
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      <MainHeader />

      <div className='flex flex-1 flex-col gap-6 p-4 pt-0'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <div className='flex items-center gap-3'>
                <h1 className='text-3xl font-bold tracking-tight'>
                  {selectedCluster.name}
                </h1>
                <Badge variant={getBadgeVariant(selectedCluster.status)}>
                  {selectedCluster.status.name}
                </Badge>
              </div>
              <p className='text-muted-foreground'>
                Cluster details and namespace overview
              </p>
            </div>
          </div>
          {!selectedCluster.is_self && (
            <div className='flex items-center gap-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  router.push(
                    `/logs?event_id=${selectedCluster.id}&event=cluster`
                  )
                }
              >
                <Activity className='mr-2 h-4 w-4' />
                Logs
              </Button>
              {(selectedCluster.status.name === 'unpublished' ||
                selectedCluster.status.name === 'maintenance') && (
                <Button size='sm' onClick={handlePublishClick}>
                  <Play className='mr-2 h-4 w-4' />
                  Publish
                </Button>
              )}
              {selectedCluster.status.name === 'active' && (
                <Button size='sm' onClick={() => setOpenDestroyConfirm(true)}>
                  <Trash2 className='mr-2 h-4 w-4' />
                  Destroy
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Cluster Overview */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-5'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Status</CardTitle>
              <Activity className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                <Badge variant={getBadgeVariant(selectedCluster.status)}>
                  {selectedCluster.status.name}
                </Badge>
              </div>
              <p className='text-xs text-muted-foreground'>
                Updated{' '}
                {new Date(selectedCluster.updated_at).toLocaleDateString()}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Workspace</CardTitle>
              <Users className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedCluster.workspace.name}
              </div>
              <p className='text-xs text-muted-foreground'>
                {selectedCluster.workspace.description}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Nodes</CardTitle>
              <Server className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedCluster.node_count}
              </div>
              <p className='text-xs text-muted-foreground'>
                {selectedCluster.size} • {selectedCluster.region}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Namespaces</CardTitle>
              <Database className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedCluster.namespaces?.length || 0}
              </div>
              <p className='text-xs text-muted-foreground'>
                {selectedCluster.namespaces?.filter(ns => ns.is_active)
                  .length || 0}{' '}
                active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Pool</CardTitle>
              <Clock className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedCluster.pool_name}
              </div>
              <p className='text-xs text-muted-foreground'>
                Created{' '}
                {new Date(selectedCluster.created_at).toLocaleDateString()}
              </p>
            </CardContent>
          </Card>
        </div>

        <Separator />

        {/* Namespaces */}
        <div className='space-y-4'>
          <h2 className='text-2xl font-bold tracking-tight'>Namespaces</h2>

          <div className='space-y-4'>
            {selectedCluster.namespaces && selectedCluster.namespaces.length > 0
              ? selectedCluster.namespaces.map(namespace => {
                  const namespaceStatus = calculateProjectStatus(namespace);
                  return (
                    <Card key={namespace.id}>
                      <Collapsible
                        open={expandedNamespaces.has(namespace.id)}
                        onOpenChange={() => toggleNamespace(namespace.id)}
                      >
                        <CollapsibleTrigger asChild>
                          <CardHeader className='cursor-pointer transition-colors'>
                            <div className='flex items-center justify-between'>
                              <div className='flex items-center gap-3'>
                                <div className='flex items-center gap-2'>
                                  {expandedNamespaces.has(namespace.id) ? (
                                    <ChevronDown className='h-4 w-4' />
                                  ) : (
                                    <ChevronRight className='h-4 w-4' />
                                  )}
                                  <CardTitle className='text-lg'>
                                    {namespace.name}
                                  </CardTitle>
                                </div>
                                <Badge
                                  variant='outline'
                                  className='h-6 flex items-center'
                                >
                                  {namespace.type}
                                </Badge>
                                <Badge
                                  variant={getBadgeVariant(namespaceStatus)}
                                  className='h-6 flex items-center'
                                >
                                  {namespaceStatus}
                                </Badge>
                              </div>
                              <div className='flex items-center gap-4'>
                                <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                                  <Play className='h-3 w-3' />
                                  <span>
                                    {namespace.deployments?.length || 0}
                                  </span>
                                  <Globe className='h-3 w-3 ml-2' />
                                  <span>{namespace.services?.length || 0}</span>
                                  <Activity className='h-3 w-3 ml-2' />
                                  <span>{namespace.ingress?.length || 0}</span>
                                </div>
                                <div className='text-sm text-muted-foreground'>
                                  {namespace.slug}
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                        </CollapsibleTrigger>

                        <CollapsibleContent>
                          <CardContent>
                            <div className='grid gap-4 md:grid-cols-3'>
                              {/* Deployments */}
                              <Card>
                                <CardHeader className='pb-3'>
                                  <div className='flex items-center gap-2'>
                                    <Play className='h-4 w-4 text-blue-500' />
                                    <CardTitle className='text-sm'>
                                      Deployments (
                                      {namespace.deployments?.length || 0})
                                    </CardTitle>
                                  </div>
                                </CardHeader>
                                <CardContent className='space-y-2'>
                                  {namespace.deployments?.map(deployment => (
                                    <div
                                      key={deployment.id}
                                      className='flex items-center justify-between p-2 rounded border'
                                    >
                                      <span className='text-sm font-medium'>
                                        {deployment.name}
                                      </span>
                                      <Badge
                                        variant={getBadgeVariant(
                                          deployment.status
                                        )}
                                        className='text-xs'
                                      >
                                        {deployment.status.name}
                                      </Badge>
                                    </div>
                                  )) || []}
                                  {(!namespace.deployments ||
                                    namespace.deployments.length === 0) && (
                                    <p className='text-sm text-muted-foreground italic'>
                                      No deployments
                                    </p>
                                  )}
                                </CardContent>
                              </Card>

                              {/* Services */}
                              <Card>
                                <CardHeader className='pb-3'>
                                  <div className='flex items-center gap-2'>
                                    <Globe className='h-4 w-4 text-green-500' />
                                    <CardTitle className='text-sm'>
                                      Services (
                                      {namespace.services?.length || 0})
                                    </CardTitle>
                                  </div>
                                </CardHeader>
                                <CardContent className='space-y-2'>
                                  {namespace.services?.map(service => (
                                    <div
                                      key={service.id}
                                      className='flex items-center justify-between p-2 rounded border'
                                    >
                                      <span className='text-sm font-medium'>
                                        {service.name}
                                      </span>
                                      <Badge
                                        variant={getBadgeVariant(
                                          service.status
                                        )}
                                        className='text-xs'
                                      >
                                        {service.status.name}
                                      </Badge>
                                    </div>
                                  )) || []}
                                  {(!namespace.services ||
                                    namespace.services.length === 0) && (
                                    <p className='text-sm text-muted-foreground italic'>
                                      No services
                                    </p>
                                  )}
                                </CardContent>
                              </Card>

                              {/* Ingress */}
                              <Card>
                                <CardHeader className='pb-3'>
                                  <div className='flex items-center gap-2'>
                                    <Activity className='h-4 w-4 text-purple-500' />
                                    <CardTitle className='text-sm'>
                                      Ingress ({namespace.ingress?.length || 0})
                                    </CardTitle>
                                  </div>
                                </CardHeader>
                                <CardContent className='space-y-2'>
                                  {namespace.ingress?.map(ingress => (
                                    <div
                                      key={ingress.id}
                                      className='flex items-center justify-between p-2 rounded border'
                                    >
                                      <span className='text-sm font-medium'>
                                        {ingress.name}
                                      </span>
                                      <Badge
                                        variant={getBadgeVariant(
                                          ingress.status
                                        )}
                                        className='text-xs'
                                      >
                                        {ingress.status.name}
                                      </Badge>
                                    </div>
                                  )) || []}
                                  {(!namespace.ingress ||
                                    namespace.ingress.length === 0) && (
                                    <p className='text-sm text-muted-foreground italic'>
                                      No ingress
                                    </p>
                                  )}
                                </CardContent>
                              </Card>
                            </div>
                          </CardContent>
                        </CollapsibleContent>
                      </Collapsible>
                    </Card>
                  );
                })
              : null}
          </div>

          {(!selectedCluster.namespaces ||
            selectedCluster.namespaces.length === 0) && (
            <div className='flex flex-col items-center justify-center py-12 text-center'>
              <Database className='h-12 w-12 text-muted-foreground mb-4' />
              <h3 className='text-lg font-semibold'>No namespaces found</h3>
              <p className='text-muted-foreground'>
                This cluster does not have any namespaces configured yet
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Confirm Publish Modal */}
      <ConfirmActionModal
        open={openPublishConfirm}
        onOpenChange={setOpenPublishConfirm}
        onConfirm={handlePublish}
        title='Confirm Publish'
        description={`Are you sure you want to publish cluster "${selectedCluster.name}"? This will apply all configurations to the cluster.`}
        actionText='Publish'
        loadingText='Publishing...'
        variant='default'
        icon={<Play className='h-5 w-5 text-blue-600' />}
      />

      {/* Confirm Destroy Modal */}
      <ConfirmActionModal
        open={openDestroyConfirm}
        onOpenChange={setOpenDestroyConfirm}
        onConfirm={handleDestroy}
        title='Confirm Destroy'
        description={`Are you sure you want to destroy cluster "${selectedCluster.name}"? This action cannot be undone and will permanently delete all cluster resources.`}
        actionText='Destroy'
        loadingText='Destroying...'
        variant='destructive'
        icon={<Trash2 className='h-5 w-5 text-red-600' />}
      />
    </div>
  );
}
