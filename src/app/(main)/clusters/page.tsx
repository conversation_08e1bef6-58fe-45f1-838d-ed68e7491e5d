'use client';

import { Plus, <PERSON>, Setting<PERSON>, Eye } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { CreateClusterModal } from '@/components/main/cluster/create-cluster-modal';
import { EditClusterModal } from '@/components/main/cluster/edit-cluster-modal';
import { MainHeader } from '@/components/main/layout/main-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useClusterStore } from '@/store/cluster/action';
import type { ClusterType } from '@/store/cluster/type';
import { useWorkspaceStore } from '@/store/workspace/action';
import { getBadgeVariant } from '@/utils/getVariant';

export default function ClustersPage() {
  const { clusters, loading, fetchClusters } = useClusterStore();
  const { selectedWorkspace, fetchWorkspaces } = useWorkspaceStore();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCluster, setSelectedCluster] = useState<ClusterType | null>(
    null
  );
  const router = useRouter();

  useEffect(() => {
    fetchWorkspaces();
  }, [fetchWorkspaces]);

  // Fetch clusters when selectedWorkspace changes
  useEffect(() => {
    if (selectedWorkspace) {
      fetchClusters(selectedWorkspace.id);
    }
  }, [selectedWorkspace, fetchClusters]);

  // Loading skeleton component
  const ClusterCardSkeleton = () => (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <Skeleton className='h-4 w-32' />
        <Skeleton className='h-4 w-4' />
      </CardHeader>
      <CardContent>
        <div className='space-y-2'>
          {[...Array(5)].map((_, i) => (
            <div key={i} className='flex items-center justify-between'>
              <Skeleton className='h-4 w-16' />
              <Skeleton className='h-4 w-12' />
            </div>
          ))}
          <div className='flex gap-2 pt-2'>
            <Skeleton className='h-8 flex-1' />
            <Skeleton className='h-8 flex-1' />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const handleEditCluster = (cluster: ClusterType) => {
    setSelectedCluster(cluster);
    setIsEditModalOpen(true);
  };

  const handleViewCluster = (clusterId: number) => {
    router.push(`/clusters/${clusterId}`);
  };

  return (
    <>
      <div className='flex flex-1 flex-col'>
        {/* Header */}
        <MainHeader />

        {/* Main Content */}
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold tracking-tight'>Clusters</h1>
              <p className='text-muted-foreground'>
                Manage and monitor your Kubernetes clusters
              </p>
            </div>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className='mr-2 h-4 w-4' />
              Add Cluster
            </Button>
          </div>

          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
            {loading ? (
              // Show loading skeletons
              [...Array(3)].map((_, i) => <ClusterCardSkeleton key={i} />)
            ) : !clusters || clusters.length === 0 ? (
              // Show empty state
              <div className='col-span-full flex flex-col items-center justify-center py-12 text-center'>
                <Server className='h-12 w-12 text-muted-foreground mb-4' />
                <h3 className='text-lg font-semibold'>No clusters found</h3>
                <p className='text-muted-foreground mb-4'>
                  Get started by creating your first cluster
                </p>
                <Button onClick={() => setIsCreateModalOpen(true)}>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Cluster
                </Button>
              </div>
            ) : (
              // Show actual clusters
              clusters.map(cluster => (
                <Card key={cluster.id}>
                  <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                    <CardTitle className='text-sm font-medium'>
                      {cluster.name}
                    </CardTitle>
                    <Server className='h-4 w-4 text-muted-foreground' />
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-2'>
                      <div className='flex items-center justify-between'>
                        <span className='text-sm text-muted-foreground'>
                          Workspace
                        </span>
                        <span className='text-sm font-medium'>
                          {cluster.workspace.name}
                        </span>
                      </div>
                      <div className='flex items-center justify-between'>
                        <span className='text-sm text-muted-foreground'>
                          Nodes
                        </span>
                        <span className='text-sm font-medium'>
                          {cluster.node_count}
                        </span>
                      </div>
                      <div className='flex items-center justify-between'>
                        <span className='text-sm text-muted-foreground'>
                          Size
                        </span>
                        <span className='text-sm font-medium'>
                          {cluster.size}
                        </span>
                      </div>
                      <div className='flex items-center justify-between'>
                        <span className='text-sm text-muted-foreground'>
                          Region
                        </span>
                        <span className='text-sm font-medium'>
                          {cluster.region}
                        </span>
                      </div>
                      <div className='flex items-center justify-between'>
                        <span className='text-sm text-muted-foreground'>
                          Pool
                        </span>
                        <span className='text-sm font-medium'>
                          {cluster.pool_name}
                        </span>
                      </div>
                      <div className='flex items-center justify-between'>
                        <span className='text-sm text-muted-foreground'>
                          Status
                        </span>
                        <Badge
                          variant={getBadgeVariant(
                            cluster.status || { name: 'unknown' }
                          )}
                        >
                          {cluster?.status?.name || 'unknown'}
                        </Badge>
                      </div>
                      <div className='flex gap-2 pt-2'>
                        <Button
                          size='sm'
                          variant='outline'
                          className='flex-1'
                          onClick={() => handleEditCluster(cluster)}
                        >
                          <Settings className='mr-2 h-3 w-3' />
                          Configure
                        </Button>
                        <Button
                          size='sm'
                          variant='default'
                          className='flex-1'
                          onClick={() => handleViewCluster(cluster.id)}
                        >
                          <Eye className='mr-2 h-3 w-3' />
                          Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>

      <CreateClusterModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
      />

      <EditClusterModal
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        cluster={selectedCluster}
      />
    </>
  );
}
