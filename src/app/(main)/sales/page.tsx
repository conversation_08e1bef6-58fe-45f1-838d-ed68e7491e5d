'use client';

import { Users, Search, X, Edit, Trash2, Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';

import { ConfirmDeleteModal } from '@/components/confirm-delete-modal';
import { MainHeader } from '@/components/main/layout/main-header';
import { CreateSaleModal } from '@/components/main/sales/create-sale-modal';
import { EditSaleModal } from '@/components/main/sales/edit-sale-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDebounce } from '@/hooks/use-debounce';
import { useSalesStore } from '@/store/sales/action';
import { GetSalesParams, SaleUserType } from '@/store/sales/type';

export default function SalesPage() {
  const { sales, loading, deleting, updating, fetchSales, deleteSale } =
    useSalesStore();

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const [emailFilter, setEmailFilter] = useState('');
  const debouncedEmailFilter = useDebounce(emailFilter, 500);
  const [isClient, setIsClient] = useState(false);

  // Modal states
  const [deletingSale, setDeletingSale] = useState<SaleUserType | null>(null);
  const [editingSale, setEditingSale] = useState<SaleUserType | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const loadSales = useCallback(() => {
    const filters: GetSalesParams = {};

    if (debouncedSearchTerm.trim()) {
      filters.name = debouncedSearchTerm.trim();
    }
    if (debouncedEmailFilter.trim()) {
      filters.email = debouncedEmailFilter.trim();
    }

    fetchSales(Object.keys(filters).length > 0 ? filters : undefined);
  }, [debouncedSearchTerm, debouncedEmailFilter, fetchSales]);

  // Load sales on mount and when filters change
  useEffect(() => {
    loadSales();
  }, [loadSales]);

  const clearFilters = () => {
    setSearchTerm('');
    setEmailFilter('');
  };

  const hasActiveFilters =
    debouncedSearchTerm.trim() || debouncedEmailFilter.trim();

  const handleEditSale = (sale: SaleUserType) => {
    setEditingSale(sale);
  };

  const handleDeleteSale = (sale: SaleUserType) => {
    setDeletingSale(sale);
  };

  const confirmDeleteSale = async () => {
    if (!deletingSale) {
      return;
    }

    try {
      const response = await deleteSale(deletingSale.id);
      if (response?.status) {
        toast.success('Sale user deleted successfully');
        setDeletingSale(null);
      } else {
        toast.error(response?.message || 'Failed to delete sale user');
      }
    } catch (error: any) {
      toast.error(
        error?.response?.data?.message || 'Failed to delete sale user'
      );
    }
  };

  // Don't render interactive elements until client-side hydration is complete
  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='space-y-2 mb-4'>
            <h1 className='text-3xl font-medium tracking-tight flex items-center gap-2'>
              <Users className='h-8 w-8' />
              Sales
            </h1>
            <p className='text-muted-foreground'>
              Manage and view all sales team members.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      {/* Header */}
      <MainHeader />

      {/* Main Content */}
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
        <div className='space-y-2 mb-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-semibold tracking-tight flex items-center gap-2'>
                <Users className='h-6 w-6' />
                Sales
              </h1>
              <p className='text-sm text-muted-foreground mt-1'>
                Manage and view all sales team members.
              </p>
            </div>
            <CreateSaleModal />
          </div>
        </div>

        {/* Filters */}
        <div className='flex flex-col lg:flex-row gap-4 mb-4'>
          {/* Search */}
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
            <Input
              placeholder='Search sales by name...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>

          {/* Email Filter */}
          <div className='relative'>
            <Input
              placeholder='Filter by username...'
              value={emailFilter}
              onChange={e => setEmailFilter(e.target.value)}
              className='w-full lg:w-48'
            />
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant='outline'
              onClick={clearFilters}
              className='flex items-center gap-2'
            >
              <X className='h-4 w-4' />
              Clear
            </Button>
          )}
        </div>

        {/* Loading State */}
        {loading && (
          <div className='flex items-center justify-center py-12'>
            <Loader2 className='h-8 w-8 animate-spin text-muted-foreground' />
          </div>
        )}

        {/* Table */}
        {!loading && (
          <div className='border rounded-lg overflow-hidden'>
            <div className='overflow-x-auto'>
              <Table className='min-w-full'>
                <TableHeader>
                  <TableRow>
                    <TableHead className='min-w-[150px] whitespace-nowrap'>
                      Name
                    </TableHead>
                    <TableHead className='min-w-[200px] whitespace-nowrap'>
                      Username
                    </TableHead>
                    <TableHead className='min-w-[120px] whitespace-nowrap'>
                      Role
                    </TableHead>
                    <TableHead className='min-w-[100px] whitespace-nowrap'>
                      Status
                    </TableHead>
                    <TableHead className='min-w-[150px] whitespace-nowrap'>
                      Description
                    </TableHead>
                    <TableHead className='min-w-[120px] whitespace-nowrap'>
                      Created
                    </TableHead>
                    <TableHead className='min-w-[100px] whitespace-nowrap text-center'>
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {!sales || sales.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={7}
                        className='text-center py-8 text-muted-foreground'
                      >
                        No sales users found.{' '}
                        {hasActiveFilters && 'Try adjusting your filters.'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    sales.map(sale => (
                      <TableRow key={sale.id}>
                        <TableCell className='font-medium whitespace-nowrap'>
                          <div
                            className='max-w-[150px] truncate'
                            title={sale.name}
                          >
                            {sale.name}
                          </div>
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          <div
                            className='max-w-[200px] truncate font-mono text-sm'
                            title={sale.email}
                          >
                            {sale.email}
                          </div>
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          <Badge variant='outline'>{sale.user_type.name}</Badge>
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          <Badge
                            variant={
                              sale.user_type.is_active ? 'default' : 'secondary'
                            }
                          >
                            {sale.user_type.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          <div
                            className='max-w-[150px] truncate text-muted-foreground'
                            title={
                              sale.user_type.description || 'No description'
                            }
                          >
                            {sale.user_type.description || '-'}
                          </div>
                        </TableCell>
                        <TableCell className='text-muted-foreground whitespace-nowrap'>
                          {new Date(sale.created_at).toLocaleDateString(
                            'en-US',
                            {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            }
                          )}
                        </TableCell>
                        <TableCell className='text-center whitespace-nowrap'>
                          <div className='flex items-center justify-center gap-1'>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleEditSale(sale)}
                              className='flex items-center gap-2'
                              disabled={updating}
                            >
                              <Edit className='h-4 w-4' />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleDeleteSale(sale)}
                              className='flex items-center gap-2 text-destructive hover:text-destructive'
                              disabled={deleting || updating}
                            >
                              <Trash2 className='h-4 w-4' />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        )}

        {/* Edit Sale Modal */}
        <EditSaleModal
          sale={editingSale}
          open={editingSale !== null}
          onOpenChange={open => {
            if (!open) {
              setEditingSale(null);
            }
          }}
        />

        {/* Delete Confirmation Modal */}
        {deletingSale && (
          <ConfirmDeleteModal
            open={deletingSale !== null}
            onOpenChange={open => {
              if (!open) {
                setDeletingSale(null);
              }
            }}
            onConfirm={confirmDeleteSale}
            title='Delete Sale User'
            itemName={deletingSale.name}
            description={`Are you sure you want to delete the sale user "${deletingSale.name}" (${deletingSale.email})? This action cannot be undone.`}
          />
        )}
      </div>
    </div>
  );
}
