export const getBadgeVariant = (
  status: string | { name: string; id?: number }
): 'default' | 'destructive' | 'secondary' | 'outline' => {
  const statusName = typeof status === 'string' ? status : status.name;
  const statusId = typeof status === 'object' && status.id ? status.id : null;

  // Handle status by name or ID
  switch (statusName) {
    case 'active':
    case 'published':
    case 'running':
      return 'default'; // Green/blue background
    case 'error':
    case 'destroyed':
      return 'destructive'; // Red background
    case 'creating':
    case 'updating':
    case 'deleting':
    case 'maintenance':
      return 'secondary'; // Gray/muted background
    case 'unpublished':
    case 'mixed':
      return 'outline'; // Border only
    default:
      // Handle by ID if name doesn't match
      if (statusId === 3) {
        return 'default'; // active/published
      }
      if (statusId === 8 || statusId === 9) {
        return 'destructive'; // error/destroyed
      }
      if (
        statusId === 2 ||
        statusId === 5 ||
        statusId === 6 ||
        statusId === 7
      ) {
        return 'secondary'; // creating/updating/maintenance/deleting
      }
      if (statusId === 1) {
        return 'outline'; // unpublished
      }
      return 'outline';
  }
};
