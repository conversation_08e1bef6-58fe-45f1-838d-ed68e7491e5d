export const calculateProjectStatus = (project: any) => {
  if (!project) {
    return 'unknown';
  }

  const allComponents = [
    ...(project.deployments || []),
    ...(project.services || []),
    ...(project.ingress || []),
  ];

  if (allComponents.length === 0) {
    return 'unpublished';
  }

  const statusCounts = allComponents.reduce(
    (acc, component) => {
      const statusId = component.status?.id;
      acc[statusId] = (acc[statusId] || 0) + 1;
      return acc;
    },
    {} as Record<number, number>
  );

  const statusIds = Object.keys(statusCounts).map(Number);

  // Helper function to convert status ID to status string
  const getStatusString = (statusId: number): string => {
    switch (statusId) {
      case 1:
        return 'unpublished';
      case 2:
        return 'creating';
      case 3:
        return 'active';
      case 5:
        return 'updating';
      case 6:
        return 'maintenance';
      case 7:
        return 'deleting';
      case 8:
        return 'error';
      case 9:
        return 'destroyed';
      default:
        return 'unknown';
    }
  };

  // Rule 1: Uniform status rule - If all components have the same status, return that status
  if (statusIds.length === 1) {
    return getStatusString(statusIds[0]);
  }

  // Rule 2: Maintenance priority - If there is at least one item with "maintenance" status
  if (statusCounts[6] && statusCounts[6] >= 1) {
    return 'maintenance';
  }

  // Rule 3: Unpublished priority - If there is at least one item with "unpublished" status (and no maintenance items)
  if (statusCounts[1] && statusCounts[1] >= 1) {
    return 'unpublished';
  }

  // For mixed statuses that don't fall under the priority rules above,
  // we still need to handle critical statuses that should take precedence
  // over the uniform status rule when components are mixed

  // Check for error (id = 8) - critical status
  if (statusCounts[8] && statusCounts[8] >= 1) {
    return 'error';
  }

  // Check for destroyed (id = 9) - critical status
  if (statusCounts[9] && statusCounts[9] >= 1) {
    return 'destroyed';
  }

  // Check for deleting (id = 7) - critical status
  if (statusCounts[7] && statusCounts[7] >= 1) {
    return 'deleting';
  }

  // Check for updating (id = 5)
  if (statusCounts[5] && statusCounts[5] >= 1) {
    return 'updating';
  }

  // Check for creating (id = 2)
  if (statusCounts[2] && statusCounts[2] >= 1) {
    return 'creating';
  }

  // Check if all remaining are active (id = 3)
  if (statusCounts[3] && statusCounts[3] === allComponents.length) {
    return 'active';
  }

  // Default fallback for any other mixed scenarios
  return 'mixed';
};
