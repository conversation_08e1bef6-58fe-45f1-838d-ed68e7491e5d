{"name": "ops-admin-app", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f .env.local next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "check-all": "npm run type-check && npm run lint && npm run format:check"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "gsap": "^3.13.0", "lucide-react": "^0.516.0", "motion": "^12.18.1", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.6", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "zod": "^3.25.74", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/axios": "^0.9.36", "@types/lodash": "^4.17.18", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "axios": "^1.10.0", "cookies-next": "^6.0.0", "dayjs": "^1.11.13", "env-cmd": "^10.1.0", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "lodash": "^4.17.21", "prettier": "^3.5.3", "react-hook-form": "^7.60.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}