name: Blacking Admin Prod CI

on:
  push:
    tags:
      - "v*.*.*"

env:
  IMAGE_NAME: devblacking3/blacking-ops-admin
  DOCKER_FILE_DIR: ./Dockerfile

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ${{ env.IMAGE_NAME }}
          tags: |
            type=semver,pattern=v{{major}}.{{minor}}.{{patch}}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v4
        with:
          context: ./
          file: ${{ env.DOCKER_FILE_DIR }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
