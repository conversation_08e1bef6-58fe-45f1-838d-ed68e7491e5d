name: Blacking Admin Staging CI
on:
  push:
    branches:
      - staging

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            devblacking3/blacking-ops-admin
          tags: |
            type=raw,value=staging-latest
            type=sha,prefix=staging-

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          context: ./
          file: ./Dockerfile
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          build-args: |
            NODE_VERSION=20-alpine

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
#  rollout:
#    needs: [ build ]
#    name: Restart Deployment
#    runs-on: ubuntu-latest
#
#    steps:
#      - name: Checkout
#        uses: actions/checkout@v4
#      # Set up the Kubernetes CLI with your DigitalOcean Kubernetes cluster.
#      - name: Set up kubectl
#        uses: matootie/dokube@v1.4.1
#        with:
#          personalAccessToken: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
#          clusterName: k8s-blacking
#          version: "1.20.15"
#      # Run any kubectl commands you want!
#      - name: Restart Deployment
#        run: kubectl rollout restart deployment blacking-admin --namespace dev

